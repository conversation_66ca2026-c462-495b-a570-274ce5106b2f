export default {
    data() {
        return {
            share: {
                title: '七宝学习助手',
                path: '/pages/index/index',
                imageUrl: '/static/share-image.png',
                desc: '快来和我一起练习听写吧',
                content: '七宝学习助手-听写助手'
            }
        }
    },
    onShareAppMessage(res) {
        return {
            title: this.share.title,
            path: this.share.path,
            imageUrl: this.share.imageUrl,
            desc: this.share.desc,
            content: this.share.content,
            success(res) {
                uni.showToast({
                    title: '分享成功',
                    icon: 'success'
                });
            },
            fail(res) {
                uni.showToast({
                    title: '分享失败',
                    icon: 'none'
                });
            }
        }
    },
    // 分享到朋友圈
    onShareTimeline(res) {
        return {
            title: this.share.title,
            query: `page=${this.share.path}`,
            imageUrl: this.share.imageUrl
        }
    }
} 