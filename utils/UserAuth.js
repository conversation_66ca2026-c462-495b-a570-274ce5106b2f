/**
 * 用户权限验证工具类
 */
class UserAuth {
  constructor() {
    this.storageKey = 'user_auth_info';
    // 临时使用的邀请码列表
    this.inviteCodes = [
      'QIBAO2024VIP',
      'STUDY2024VIP',
      'DICT2024VIP'
    ];
    this.inviteCode = null;
    this.isVip = false;
    this.dictationCount = 0; // 添加听写次数计数器
    this.lastDictationDate = null; // 添加最后听写日期
    this.inDictation = false; // 是否正在听写中
  }

  /**
   * 初始化用户信息
   */
  async init() {
    try {
      // 获取存储的权限信息
      let authInfo = uni.getStorageSync(this.storageKey) || {};
      console.log('authInfohu获取到的用户信息', authInfo);
      
      // 如果是新的一天，重置普通用户的使用次数
      if (this._isNewDay(authInfo.lastUseDate) && !authInfo.isVip) {
        // 重置使用次数
        authInfo.usageCount = 0;
        authInfo.lastUseDate = null;
        authInfo.inDictation = false; // 重置听写状态
        
        // 重置听写相关状态
        this.dictationCount = 0;
        this.lastDictationDate = null;
        this.inDictation = false;
        
        // 保存更新后的权限信息
        uni.setStorageSync(this.storageKey, authInfo);
      }

      // 从本地存储获取邀请码
      const savedInviteCode = uni.getStorageSync('inviteCode');
      if (savedInviteCode) {
        this.inviteCode = savedInviteCode;
        this.isVip = this.verifyInviteCode(savedInviteCode);
      }
      
    

      return true;
    } catch (error) {
      console.error('初始化用户信息失败:', error);
      return false;
    }
  }

  /**
   * 验证邀请码
   * @param {string} code - 邀请码
   * @returns {Object} - 验证结果
   */
  verifyInviteCode(code) {
    try {
      // 验证邀请码
      const isValid = this.inviteCodes.includes(code.trim().toUpperCase());
      
      if (isValid) {
        // 更新存储信息
        const authInfo = uni.getStorageSync(this.storageKey) || {};
        authInfo.isVip = true;
        uni.setStorageSync(this.storageKey, authInfo);
        
        return {
          success: true,
          message: '验证成功'
        };
      }
      
      return {
        success: false,
        message: '邀请码无效，请重新输入'
      };
    } catch (error) {
      console.error('验证邀请码失败:', error);
      return {
        success: false,
        message: '验证失败，请稍后重试'
      };
    }
  }

  /**
   * 检查功能使用权限
   * @param {string} feature - 功能名称
   * @returns {Object} - 检查结果
   */
  async checkFeaturePermission(feature) {
    try {
      // 获取存储的权限信息
      const authInfo = uni.getStorageSync(this.storageKey) || {};
      console.log('authInfohu获取到的用户信息', authInfo);
      // 检查是否是 VIP 用户
      if (authInfo.isVip) {
        return {
          allowed: true,
          message: 'VIP用户不受限制'
        };
      }

      // 检查使用次数
      const usageCount = authInfo.usageCount || 0;
      console.log('usageCount！！！', usageCount);
      const maxCount = this._getFeatureMaxCount(feature);

      if (usageCount >= maxCount) {
        return {
          allowed: false,
          message: `每人每天只能使用${maxCount}次${this._getFeatureName(feature)}，请明天再来`
        };
      }

      return {
        allowed: true,
        message: '验证通过'
      };
    } catch (error) {
      console.error('检查权限失败:', error);
      return {
        allowed: false,
        message: '验证失败，请稍后重试'
      };
    }
  }

  /**
   * 标记听写开始
   */
  markDictationStart() {
    try {
      const today = new Date().toDateString();
      
      // 获取并更新权限信息
      const authInfo = uni.getStorageSync(this.storageKey) || {};
      authInfo.inDictation = true;
      authInfo.lastUseDate = today;
      
      // 增加听写次数
      this.dictationCount++;
      authInfo.usageCount = this.dictationCount;
      
      
      
      // 保存听写数据
      uni.setStorageSync(this.storageKey, authInfo);
      
      console.log('保存听写次数:', authInfo);
      
      
    } catch (error) {
      console.error('标记听写开始失败:', error);
    }
  }

  /**
   * 标记听写结束并记录使用次数 暂时没用
   * @param {string} feature - 功能名称
   */
  async markDictationComplete(feature = 'dictation') {
    try {
      const authInfo = uni.getStorageSync(this.storageKey) || {};
      
      // VIP用户不记录使用次数
      if (authInfo.isVip) {
        return;
      }
      
      // 如果不是在听写中，不记录使用次数
      if (!authInfo.inDictation) {
        return;
      }
      
      if (!authInfo.usageCount) {
        authInfo.usageCount = {};
      }
      
      if (!authInfo.usageCount[feature]) {
        authInfo.usageCount[feature] = 0;
      }
      
      authInfo.usageCount[feature]++;
      authInfo.lastUseDate = new Date().toDateString();
      authInfo.inDictation = false; // 重置听写状态
      
      uni.setStorageSync(this.storageKey, authInfo);
    } catch (error) {
      console.error('记录使用次数失败:', error);
    }
  }



  /**
   * 检查是否是新的一天
   * @private
   */
  _isNewDay(lastUseDate) {
    if (!lastUseDate) return true;
    return new Date().toDateString() !== lastUseDate;
  }

  /**
   * 获取功能最大使用次数
   * @private
   */
  _getFeatureMaxCount(feature) {
    const limits = {
      dictation: 1 // 听写功能每天限制使用1次
    };
    return limits[feature] || 0;
  }

  /**
   * 获取功能名称
   * @private
   */
  _getFeatureName(feature) {
    const names = {
      dictation: '听写功能'
    };
    return names[feature] || feature;
  }

  /**
   * 获取用户VIP状态
   * @returns {boolean} - 是否是VIP用户
   */
  isVipUser() {
    try {
      const authInfo = uni.getStorageSync(this.storageKey) || {};
      return !!authInfo.isVip;
    } catch (error) {
      console.error('获取VIP状态失败:', error);
      return false;
    }
  }



  // 设置邀请码
  setInviteCode(code) {
    if (this.verifyInviteCode(code)) {
      this.inviteCode = code;
      this.isVip = true;
      try {
        uni.setStorageSync('inviteCode', code);
      } catch (error) {
        console.error('保存邀请码失败:', error);
      }
      return true;
    }
    return false;
  }

  /**
   * 清除 VIP 状态
   */
  clearVipStatus() {
    try {
      // 清除本地存储的邀请码
      uni.removeStorageSync('inviteCode');
      
      // 清除权限信息中的 VIP 状态
      let authInfo = {};
      try {
        const storedInfo = uni.getStorageSync(this.storageKey);
        authInfo = storedInfo ? storedInfo : {};
      } catch (e) {
        console.log('解析存储数据失败，使用默认值', e);
        authInfo = {};
      }
      
      // 更新 VIP 状态
      authInfo.isVip = false;
      
      // 保存更新后的信息
      uni.setStorageSync(this.storageKey, authInfo);
      
      // 重置实例中的状态
      this.inviteCode = null;
      this.isVip = false;
      
      return {
        success: true,
        message: 'VIP状态已清除'
      };
    } catch (error) {
      console.error('清除VIP状态失败:', error);
      return {
        success: false,
        message: '清除失败，请稍后重试'
      };
    }
  }

  /**
   * 清空使用次数
   */
  clearUsageCount() {
    try {
      // 获取存储的权限信息
      let authInfo = {};
      try {
        const storedInfo = uni.getStorageSync(this.storageKey);
        authInfo = storedInfo ? storedInfo : {};
      } catch (e) {
        console.log('解析存储数据失败，使用默认值', e);
        authInfo = {};
      }
      
      // 重置使用次数
      authInfo.usageCount = 0;
      authInfo.lastUseDate = null;
      authInfo.inDictation = false;
      
      // 重置实例状态
      this.dictationCount = 0;
      this.lastDictationDate = null;
      this.inDictation = false;
      
      // 保存更新后的信息
      uni.setStorageSync(this.storageKey, authInfo);
      
      return {
        success: true,
        message: '使用次数已清空'
      };
    } catch (error) {
      console.error('清空使用次数失败:', error);
      return {
        success: false,
        message: '清空失败，请稍后重试'
      };
    }
  }

  /**
   * 安全获取存储的权限信息
   * @private
   */
  _getStoredAuthInfo() {
    try {
      const storedInfo = uni.getStorageSync(this.storageKey);
      return storedInfo ? storedInfo: {};
    } catch (e) {
      console.log('解析存储数据失败，使用默认值', e);
      return {};
    }
  }

  /**
   * 安全保存权限信息
   * @private
   */
  _saveAuthInfo(authInfo) {
    try {
      uni.setStorageSync(this.storageKey, authInfo);
      return true;
    } catch (e) {
      console.error('保存权限信息失败', e);
      return false;
    }
  }
}

export const userAuth = new UserAuth(); 