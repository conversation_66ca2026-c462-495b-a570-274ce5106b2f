/**
 * 翻译工具
 * 使用 luckycola.com.cn 的 API 进行翻译
 */

// API 地址
const GET_KEY_API = 'https://luckycola.com.cn/ai/getColaKey';
const TRANSLATE_API = 'https://luckycola.com.cn/tools/fanyi';

// 用户配置
const CONFIG = {
  uid: '9KY8tC1744526280754M1l5RndAtz', // 用户唯一标识，可前往官网“个人中心”获取
  appKey: '67fb5c0396e5824fd8760cdb' // 用户调用唯一凭证，可前往官网“个人中心”获取
};

// 缓存 key 和过期时间
let colaKey = null;
let keyExpireTime = 0;
let keyPromise = null; // 缓存获取 key 的 Promise

/**
 * 获取翻译所需的 ColaKey
 * @returns {Promise<string>} - 返回 ColaKey 的 Promise
 */
const getColaKey = () => {
  // 检查配置
  if (!CONFIG.uid || !CONFIG.appKey) {
    return Promise.reject(new Error('请先配置 uid 和 appKey，可前往官网“个人中心”获取'));
  }

  // 如果已有有效的 key，直接返回
  const now = Date.now();
  if (colaKey && now < keyExpireTime) {
    return Promise.resolve(colaKey);
  }

  // 如果正在获取 key，返回缓存的 Promise
  if (keyPromise) {
    return keyPromise;
  }

  // 创建新的 Promise 并缓存
  keyPromise = new Promise(async (resolve, reject) => {
    try {
      // 请求新的 key
      const response = await uni.request({
        url: GET_KEY_API,
        method: 'POST',
        data: {
          uid: CONFIG.uid,
          appKey: CONFIG.appKey
        },
        header: {
          'content-type': 'application/json'
        }
      });

      // 处理响应
      if (response.statusCode === 200) {
        const data = response.data;
        if (data.code === 0 && data.data) {
          // 保存 ColaKey
          colaKey = data.data.cola_key;
          // 设置 key 的过期时间，使用响应中的过期时间
          keyExpireTime = data.data.end_time;

          console.log('ColaKey 获取成功，过期时间：', data.data.end_time_str);
          resolve(colaKey);
        } else {
          reject(new Error(`获取 ColaKey 失败: ${data.msg || '未知错误'}`));
        }
      } else {
        reject(new Error(`请求失败，状态码: ${response.statusCode}`));
      }
    } catch (error) {
      console.error('获取 ColaKey 失败:', error);
      reject(error);
    } finally {
      // 无论成功还是失败，都清除 Promise 缓存
      // 这样如果失败了，下次还可以重试
      setTimeout(() => {
        keyPromise = null;
      }, 0);
    }
  });

  return keyPromise;
};

/**
 * 翻译单词列表
 * @param {Array} words - 要翻译的单词列表
 * @param {String} direction - 翻译方向，'en2zh'（英文到中文）或 'zh2en'（中文到英文）
 * @returns {Promise<Array>} - 返回翻译结果的 Promise
 */
export const translateWords = async (words, direction = 'en2zh') => {
  try {
    if (!words || !Array.isArray(words) || words.length === 0) {
      return Promise.reject(new Error('单词列表不能为空'));
    }

    // 验证翻译方向
    if (direction !== 'en2zh' && direction !== 'zh2en') {
      return Promise.reject(new Error('翻译方向无效，必须是 "en2zh" 或 "zh2en"'));
    }

    // 获取 ColaKey
    const colaKey = await getColaKey();

    // 准备请求参数
    const params = {
      cola_key: colaKey,
      words: words.join(','),
      direction: direction
    };

    // 发送请求
    const response = await uni.request({
      url: TRANSLATE_API,
      method: 'POST',
      data: params,
      header: {
        'content-type': 'application/json'
      }
    });

    // 处理响应
    if (response.statusCode === 200) {
      const data = response.data;
      if (data.code === 0) {
        return data.data;
      } else {
        return Promise.reject(new Error(`API 错误: ${data.msg || '未知错误'}`));
      }
    } else {
      return Promise.reject(new Error(`请求失败，状态码: ${response.statusCode}`));
    }
  } catch (error) {
    console.error('翻译失败:', error);
    return Promise.reject(error);
  }
};

/**
 * 单个单词翻译
 * @param {String} word - 要翻译的单词
 * @param {String} direction - 翻译方向，'en2zh'（英文到中文）或 'zh2en'（中文到英文）
 * @returns {Promise<String>} - 返回翻译结果的 Promise
 */
export const translateWord = async (word, direction = 'en2zh') => {
  try {
    if (!word || typeof word !== 'string') {
      return Promise.reject(new Error('单词不能为空'));
    }

    const results = await translateWords([word], direction);
    return results[0];
  } catch (error) {
    console.error('单词翻译失败:', error);
    return Promise.reject(error);
  }
};

/**
 * 检测语言类型
 * @param {String} text - 要检测的文本
 * @returns {String} - 返回语言类型，'en'（英文）或 'zh'（中文）
 */
export const detectLanguage = (text) => {
  if (!text) return '';

  // 简单的语言检测：如果包含中文字符，则认为是中文
  const zhPattern = /[\u4e00-\u9fa5]/;
  return zhPattern.test(text) ? 'zh' : 'en';
};

/**
 * 文本翻译
 * @param {String} text - 要翻译的文本
 * @param {String} fromLang - 源语言，'ZH'（中文）或 'EN'（英文）
 * @param {String} toLang - 目标语言，'ZH'（中文）或 'EN'（英文）
 * @returns {Promise<Object>} - 返回翻译结果的 Promise
 */
export const translateText = async (text, fromLang = 'ZH', toLang = 'EN') => {
  try {
    if (!text) {
      return Promise.reject(new Error('文本不能为空'));
    }

    // 验证语言参数
    if (fromLang !== 'ZH' && fromLang !== 'EN') {
      return Promise.reject(new Error('源语言无效，必须是 "ZH" 或 "EN"'));
    }

    if (toLang !== 'ZH' && toLang !== 'EN') {
      return Promise.reject(new Error('目标语言无效，必须是 "ZH" 或 "EN"'));
    }

    // 获取 ColaKey
    const colaKey = await getColaKey();

    // 准备请求参数
    const params = {
      ColaKey: colaKey,
      text: text,
      fromlang: fromLang,
      tolang: toLang
    };

    // 发送请求
    const response = await uni.request({
      url: TRANSLATE_API,
      method: 'POST',
      data: params,
      header: {
        'content-type': 'application/json'
      }
    });

    // 处理响应
    if (response.statusCode === 200) {
      const data = response.data;
      if (data.code === 0) {
        return data.data;
      } else {
        return Promise.reject(new Error(`API 错误: ${data.msg || '未知错误'}`));
      }
    } else {
      return Promise.reject(new Error(`请求失败，状态码: ${response.statusCode}`));
    }
  } catch (error) {
    console.error('文本翻译失败:', error);
    return Promise.reject(error);
  }
};

/**
 * 智能翻译（自动检测语言并翻译）
 * @param {String} text - 要翻译的文本
 * @returns {Promise<String>} - 返回翻译结果的 Promise
 */
export const smartTranslate = async (text) => {
  try {
    if (!text) {
      return Promise.reject(new Error('文本不能为空'));
    }

    const sourceLanguage = detectLanguage(text);
    const fromLang = sourceLanguage.toUpperCase();
    const toLang = fromLang === 'ZH' ? 'EN' : 'ZH';

    const result = await translateText(text, fromLang, toLang);
    return result.dst;
  } catch (error) {
    console.error('智能翻译失败:', error);
    return Promise.reject(error);
  }
};

/**
 * 预加载 ColaKey
 * 在模块加载时自动执行，无需手动调用
 */
const preloadColaKey = () => {
  // 异步预加载，不阻塞其他操作
  setTimeout(() => {
    getColaKey().then(() => {
      console.log('ColaKey 预加载成功');
    }).catch(error => {
      console.error('ColaKey 预加载失败:', error);
    });
  }, 0);
};

// 自动预加载 ColaKey
preloadColaKey();

export default {
  translateWords,
  translateWord,
  translateText,
  detectLanguage,
  smartTranslate
};
