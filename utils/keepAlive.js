/**
 * 应用保活工具类
 * 用于防止长时间计时器被系统杀死
 */
class KeepAlive {
  constructor() {
    this.isActive = false;
    this.keepAliveTimer = null;
    this.heartbeatTimer = null;
    this.lastActiveTime = Date.now();
    this.callbacks = [];
    
    // 绑定方法
    this.handleVisibilityChange = this.handleVisibilityChange.bind(this);
    this.handleAppShow = this.handleAppShow.bind(this);
    this.handleAppHide = this.handleAppHide.bind(this);
  }

  /**
   * 启动保活机制
   */
  start() {
    if (this.isActive) return;
    
    console.log('启动应用保活机制');
    this.isActive = true;
    this.lastActiveTime = Date.now();
    
    // 启动心跳检测
    this.startHeartbeat();
    
    // 启动保活定时器
    this.startKeepAliveTimer();
    
    // 监听页面可见性变化
    this.setupVisibilityListener();
    
    // 监听应用生命周期
    this.setupAppLifecycle();
    
    // 请求忽略电池优化（Android）
    this.requestBatteryOptimization();
  }

  /**
   * 停止保活机制
   */
  stop() {
    if (!this.isActive) return;
    
    console.log('停止应用保活机制');
    this.isActive = false;
    
    // 清理定时器
    if (this.keepAliveTimer) {
      clearInterval(this.keepAliveTimer);
      this.keepAliveTimer = null;
    }
    
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
    
    // 移除监听器
    this.removeListeners();
  }

  /**
   * 添加回调函数
   */
  addCallback(callback) {
    if (typeof callback === 'function') {
      this.callbacks.push(callback);
    }
  }

  /**
   * 移除回调函数
   */
  removeCallback(callback) {
    const index = this.callbacks.indexOf(callback);
    if (index > -1) {
      this.callbacks.splice(index, 1);
    }
  }

  /**
   * 启动心跳检测
   */
  startHeartbeat() {
    this.heartbeatTimer = setInterval(() => {
      this.lastActiveTime = Date.now();
      console.log('保活心跳:', new Date().toLocaleTimeString());
      
      // 执行回调函数
      this.callbacks.forEach(callback => {
        try {
          callback('heartbeat');
        } catch (error) {
          console.error('保活回调执行失败:', error);
        }
      });
    }, 10000); // 每10秒一次心跳
  }

  /**
   * 启动保活定时器
   */
  startKeepAliveTimer() {
    this.keepAliveTimer = setInterval(() => {
      // 执行一些轻量级操作来保持应用活跃
      this.performKeepAliveActions();
    }, 30000); // 每30秒执行一次
  }

  /**
   * 执行保活操作
   */
  performKeepAliveActions() {
    try {
      // 1. 更新存储时间戳
      uni.setStorageSync('lastKeepAliveTime', Date.now());
      
      // 2. 触发一个微小的界面更新
      this.triggerMinimalUpdate();
      
      // 3. 检查系统时间
      this.checkSystemTime();
      
      console.log('执行保活操作:', new Date().toLocaleTimeString());
    } catch (error) {
      console.error('保活操作失败:', error);
    }
  }

  /**
   * 触发微小的界面更新
   */
  triggerMinimalUpdate() {
    // 通过发送自定义事件来触发界面更新
    uni.$emit('keepAliveUpdate', {
      timestamp: Date.now()
    });
  }

  /**
   * 检查系统时间
   */
  checkSystemTime() {
    const now = Date.now();
    const timeDiff = now - this.lastActiveTime;
    
    if (timeDiff > 35000) { // 超过35秒没有活动
      console.warn('检测到可能的时间跳跃:', timeDiff);
      this.callbacks.forEach(callback => {
        try {
          callback('timeJump', Math.floor(timeDiff / 1000));
        } catch (error) {
          console.error('时间跳跃回调执行失败:', error);
        }
      });
    }
    
    this.lastActiveTime = now;
  }

  /**
   * 设置页面可见性监听
   */
  setupVisibilityListener() {
    // #ifdef H5
    document.addEventListener('visibilitychange', this.handleVisibilityChange);
    // #endif
  }

  /**
   * 设置应用生命周期监听
   */
  setupAppLifecycle() {
    uni.onAppShow(this.handleAppShow);
    uni.onAppHide(this.handleAppHide);
  }

  /**
   * 处理页面可见性变化
   */
  handleVisibilityChange() {
    // #ifdef H5
    if (document.hidden) {
      console.log('页面隐藏');
      this.callbacks.forEach(callback => {
        try {
          callback('pageHide');
        } catch (error) {
          console.error('页面隐藏回调执行失败:', error);
        }
      });
    } else {
      console.log('页面显示');
      this.callbacks.forEach(callback => {
        try {
          callback('pageShow');
        } catch (error) {
          console.error('页面显示回调执行失败:', error);
        }
      });
    }
    // #endif
  }

  /**
   * 处理应用显示
   */
  handleAppShow() {
    console.log('应用回到前台');
    this.lastActiveTime = Date.now();
    
    this.callbacks.forEach(callback => {
      try {
        callback('appShow');
      } catch (error) {
        console.error('应用显示回调执行失败:', error);
      }
    });
  }

  /**
   * 处理应用隐藏
   */
  handleAppHide() {
    console.log('应用进入后台');
    
    this.callbacks.forEach(callback => {
      try {
        callback('appHide');
      } catch (error) {
        console.error('应用隐藏回调执行失败:', error);
      }
    });
  }

  /**
   * 请求忽略电池优化
   */
  requestBatteryOptimization() {
    // #ifdef APP-PLUS
    try {
      // Android平台请求忽略电池优化
      if (uni.getSystemInfoSync().platform === 'android') {
        console.log('请求忽略电池优化');
        // 这里可以添加原生插件调用
      }
    } catch (error) {
      console.error('请求电池优化失败:', error);
    }
    // #endif
  }

  /**
   * 移除所有监听器
   */
  removeListeners() {
    // #ifdef H5
    document.removeEventListener('visibilitychange', this.handleVisibilityChange);
    // #endif
    
    // 注意：uni.offAppShow 和 uni.offAppHide 在某些版本中可能不存在
    try {
      uni.offAppShow(this.handleAppShow);
      uni.offAppHide(this.handleAppHide);
    } catch (error) {
      console.warn('移除应用生命周期监听失败:', error);
    }
  }

  /**
   * 获取保活状态
   */
  getStatus() {
    return {
      isActive: this.isActive,
      lastActiveTime: this.lastActiveTime,
      hasHeartbeat: !!this.heartbeatTimer,
      hasKeepAlive: !!this.keepAliveTimer,
      callbackCount: this.callbacks.length
    };
  }
}

// 创建单例实例
const keepAlive = new KeepAlive();

export default keepAlive;
