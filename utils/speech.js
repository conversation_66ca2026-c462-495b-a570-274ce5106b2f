import { CreateTTSSpeaker } from '@/uni_modules/xwq-tts-speaker/js_sdk/index.js';

// 语音朗读工具类
class SpeechUtil {
  constructor() {
    // 初始化状态
    this.isApp = this.checkIsApp();
    this.TTS = null;
    this.audioContext = null;
    this.retryCount = 0;
    this.maxRetries = 2;
    
    if (this.isApp) {
      try {
        this.TTS = CreateTTSSpeaker();
      } catch (e) {
        console.error('TTS初始化失败:', e);
      }
    }
  }

  // 检查是否是APP环境
  checkIsApp() {
    // #ifdef APP-PLUS
    return true;
    // #endif
    return false;
  }

  /**
   * 统一的语音接口
   * @param {string|Object} options - 要朗读的文本或配置对象
   */
  speak(options) {
    const text = typeof options === 'string' ? options : options.text;
    const lang = typeof options === 'string' ? this.detectLanguage(text) : (options.lang || this.detectLanguage(text));
    const rate = typeof options === 'string' ? 0.8 : (options.rate || 0.8);

    // 小程序环境
    // #ifdef MP-WEIXIN
    return new Promise((resolve, reject) => {
      try {
        // 使用本地缓存避免重复请求
        const cacheKey = `tts_${lang}_${text}`;
        const cached = uni.getStorageSync(cacheKey);
        
        if (cached) {
          const innerAudioContext = uni.createInnerAudioContext();
          innerAudioContext.src = cached;
          
          innerAudioContext.onPlay(() => {
            console.log('开始播放缓存音频:', text);
          });
          
          innerAudioContext.onError((err) => {
            console.error('缓存音频播放失败:', err);
            // 清除缓存，重新合成
            uni.removeStorageSync(cacheKey);
            this.synthesizeAndPlay(text, lang, resolve, reject);
          });
          
          innerAudioContext.onEnded(() => {
            console.log('缓存音频播放结束:', text);
            innerAudioContext.destroy();
            resolve();
          });
          
          innerAudioContext.play();
          return;
        }

        this.synthesizeAndPlay(text, lang, resolve, reject);
      } catch (error) {
        console.error('语音播放错误:', error);
        reject(error);
      }
    });
    // #endif

    // APP环境使用TTS插件
    // #ifdef APP-PLUS
    return new Promise((resolve, reject) => {
      if (!this.TTS) {
        this.useOnlineTTS(text, lang).then(resolve).catch(reject);
        return;
      }

      try {
        // #ifdef APP-PLUS-IOS
        // iOS平台
        this.TTS.start({
          content: text,
          speechRate: rate,
          pitch: 1.0,
          language: lang,
          finish: () => {
            resolve();
          }
        });
        // #endif

        // #ifdef APP-PLUS-ANDROID
        // Android平台
        this.useOnlineTTS(text, lang).then(resolve).catch(reject);
        // #endif
      } catch (e) {
        console.error('Speech error:', e);
        this.useOnlineTTS(text, lang).then(resolve).catch(reject);
      }
    });
    // #endif

    // H5环境使用Web Speech API
    // #ifdef H5
    return new Promise((resolve, reject) => {
      try {
        const utterance = new SpeechSynthesisUtterance(text);
        utterance.lang = lang;
        utterance.rate = rate;
        utterance.pitch = 1;
        utterance.volume = 1;

        utterance.onend = () => {
          resolve();
        };

        utterance.onerror = (err) => {
          console.error('Speech error:', err);
          this.useOnlineTTS(text, lang).then(resolve).catch(reject);
        };

        window.speechSynthesis.speak(utterance);
      } catch (error) {
        console.error('Speech synthesis failed:', error);
        this.useOnlineTTS(text, lang).then(resolve).catch(reject);
      }
    });
    // #endif

    // 其他环境使用在线TTS
    return this.useOnlineTTS(text, lang);
  }

  useOnlineTTS(text, lang) {
    return new Promise((resolve, reject) => {
      try {
        // 如果已经有音频上下文，先销毁
        if (this.audioContext) {
          this.audioContext.destroy();
          this.audioContext = null;
        }
        
        this.audioContext = uni.createInnerAudioContext();
        
        // #ifdef MP-WEIXIN
        // 微信小程序环境使用微信云开发 TTS
        wx.cloud.getTempFileURL({
          fileList: [{
            fileID: 'cloud://xxx.xxx/tts/' + encodeURIComponent(text) + '.mp3',
            maxAge: 60 * 60, // 有效期一小时
          }],
          success: res => {
            this.audioContext.src = res.fileList[0].tempFileURL;
            this.audioContext.play();
          },
          fail: err => {
            console.error('获取语音文件失败:', err);
            reject(err);
          }
        });
        // #endif

        // #ifndef MP-WEIXIN
        // 其他环境继续使用在线 TTS
        if (lang === 'zh-CN') {
          this.audioContext.src = `https://tts.baidu.com/text2audio?lan=zh&ie=UTF-8&spd=5&text=${encodeURIComponent(text)}`;
        } else {
          this.audioContext.src = `https://dict.youdao.com/dictvoice?audio=${encodeURIComponent(text)}&type=2`;
        }
        // #endif

        this.audioContext.onPlay(() => {
          console.log('开始播放:', text);
          this.retryCount = 0;
        });

        this.audioContext.onError((res) => {
          console.error('Audio error:', res);
          if (this.retryCount < this.maxRetries) {
            this.retryCount++;
            console.log('尝试使用备用服务，重试次数:', this.retryCount);
            this.audioContext.destroy();
            this.audioContext = null;
            setTimeout(() => {
              this.useBackupTTS(text, lang).then(resolve).catch(reject);
            }, 100);
          } else {
            this.audioContext.destroy();
            this.audioContext = null;
            this.retryCount = 0;
            reject(res);
          }
        });

        this.audioContext.onEnded(() => {
          console.log('播放结束:', text);
          this.audioContext.destroy();
          this.audioContext = null;
          this.retryCount = 0;
          resolve();
        });

        // #ifndef MP-WEIXIN
        console.log('准备播放:', text, '语言:', lang);
        this.audioContext.play();
        // #endif
      } catch (error) {
        console.error('Create audio context error:', error);
        if (this.audioContext) {
          this.audioContext.destroy();
          this.audioContext = null;
        }
        reject(error);
      }
    });
  }

  // 合成并播放语音
  synthesizeAndPlay(text, lang, resolve, reject) {
    // #ifdef MP-WEIXIN
    // 英文使用有道词典服务
    if (lang !== 'zh-CN') {
      const innerAudioContext = uni.createInnerAudioContext();
      innerAudioContext.src = `https://dict.youdao.com/dictvoice?audio=${encodeURIComponent(text)}&type=2`;
      
      innerAudioContext.onPlay(() => {
        console.log('开始播放英文:', text);
      });
      
      innerAudioContext.onError((err) => {
        console.error('英文播放失败:', err);
        innerAudioContext.destroy();
        // 如果有道服务失败，尝试使用备用服务
        this.useBackupTTS(text, lang).then(resolve).catch(reject);
      });
      
      innerAudioContext.onEnded(() => {
        console.log('英文播放结束:', text);
        innerAudioContext.destroy();
        resolve();
      });
      
      innerAudioContext.play();
      return;
    }
    
    // 中文使用微信原生语音合成
    wx.speechSynthesis({
      text: text,
      lang: 'zh_CN',
      success: (res) => {
        console.log('中文语音合成成功:', res);
        
        // 缓存合成结果
        const cacheKey = `tts_${lang}_${text}`;
        uni.setStorageSync(cacheKey, res.filename);
        
        // 创建音频上下文播放
        const innerAudioContext = uni.createInnerAudioContext();
        innerAudioContext.src = res.filename;
        
        innerAudioContext.onPlay(() => {
          console.log('开始播放中文:', text);
        });
        
        innerAudioContext.onError((err) => {
          console.error('中文播放失败:', err);
          innerAudioContext.destroy();
          // 如果原生服务失败，尝试使用备用服务
          this.useBackupTTS(text, lang).then(resolve).catch(reject);
        });
        
        innerAudioContext.onEnded(() => {
          console.log('中文播放结束:', text);
          innerAudioContext.destroy();
          resolve();
        });
        
        innerAudioContext.play();
      },
      fail: (err) => {
        console.error('中文语音合成失败:', err);
        // 如果合成失败，尝试使用备用服务
        this.useBackupTTS(text, lang).then(resolve).catch(reject);
      }
    });
    // #endif
  }

  // 添加备用TTS服务
  useBackupTTS(text, lang) {
    return new Promise((resolve, reject) => {
      try {
        const innerAudioContext = uni.createInnerAudioContext();
        
        // 使用备用服务
        let url;
        if (lang === 'zh-CN') {
          // 使用百度翻译 TTS 服务
          url = `https://fanyi.baidu.com/gettts?lan=zh&text=${encodeURIComponent(text)}&spd=5&source=web`;
        } else {
          // 使用有道词典服务
          url = `https://dict.youdao.com/dictvoice?audio=${encodeURIComponent(text)}&type=2`;
        }

        innerAudioContext.src = url;
        
        innerAudioContext.onPlay(() => {
          console.log('备用服务开始播放:', text);
        });
        
        innerAudioContext.onError((err) => {
          console.error('备用服务播放失败:', err);
          innerAudioContext.destroy();
          reject(err);
        });
        
        innerAudioContext.onEnded(() => {
          console.log('备用服务播放结束:', text);
          innerAudioContext.destroy();
          resolve();
        });
        
        innerAudioContext.play();
      } catch (error) {
        console.error('备用服务错误:', error);
        reject(error);
      }
    });
  }

  // 检测文本语言
  detectLanguage(text) {
    // 如果包含中文字符就使用中文
    if (/[\u4e00-\u9fa5]/.test(text)) {
      return 'zh-CN';
    }
    return 'en-US';
  }

  /**
   * 停止播放
   */
  stop() {
    // 停止在线TTS
    if (this.audioContext) {
      try {
        this.audioContext.stop();
        this.audioContext.destroy();
        this.audioContext = null;
      } catch (e) {
        console.error('Stop audio context error:', e);
      }
    }

    // #ifdef APP-PLUS
    try {
      if (this.TTS) {
        // #ifdef APP-PLUS-IOS
        this.TTS.stop();
        // #endif

        // #ifdef APP-PLUS-ANDROID
        this.TTS.stopSpeak();
        // #endif
      }
    } catch (e) {
      console.error('Stop speaking error:', e);
    }
    // #endif

    // #ifdef H5
    if ('speechSynthesis' in window) {
      window.speechSynthesis.cancel();
    }
    // #endif
  }
}

// 导出单例
export const speech = new SpeechUtil();

// 修改 checkSpeechSupport 方法
export function checkSpeechSupport() {
  return new Promise((resolve) => {
    // #ifdef H5
    if ('speechSynthesis' in window) {
      // 等待声音列表加载
      if (speechSynthesis.getVoices().length > 0) {
        resolve(true);
      } else {
        speechSynthesis.onvoiceschanged = () => {
          resolve(true);
        };
      }
    } else {
      resolve(true); // 即使不支持也返回true，因为可以使用在线TTS
    }
    // #endif

    // #ifdef MP-WEIXIN || APP-PLUS
    // 小程序和APP环境使用在线TTS
    resolve(true);
    // #endif

    // 其他环境
    resolve(true);
  });
} 