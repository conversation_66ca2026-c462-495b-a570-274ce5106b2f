/**
 * 错题本管理工具类
 * 提供错题本的增删改查功能
 */
class ErrorBookManager {
  constructor() {
    this.errorWords = {
      chinese: [],
      english: []
    };
    this.maxCorrectNeeded = 3; // 需要正确的最大次数
    this.storageKeyPrefix = 'qibao_errorBook_'; // 存储键前缀

    // 初始化错题本
    this.initErrorBook();
  }

  /**
   * 初始化错题本
   */
  initErrorBook() {
    try {
      // 使用独立的存储键
      let chineseErrors = uni.getStorageSync(`${this.storageKeyPrefix}chinese`) || [];
      let englishErrors = uni.getStorageSync(`${this.storageKeyPrefix}english`) || [];

      // 过滤掉已经达到最大正确次数的词语
      chineseErrors = chineseErrors.filter(word => word.correctCount < word.maxCorrectNeeded);
      englishErrors = englishErrors.filter(word => word.correctCount < word.maxCorrectNeeded);

      this.errorWords = {
        chinese: chineseErrors,
        english: englishErrors
      };

      // 保存过滤后的数据
      this.saveErrorBook();

      console.log('错题本初始化数据:', {
        chinese: {
          count: chineseErrors.length,
          words: chineseErrors
        },
        english: {
          count: englishErrors.length,
          words: englishErrors
        }
      });

      return this.errorWords;
    } catch (e) {
      console.error('加载错题本失败:', e);
      return {
        chinese: [],
        english: []
      };
    }
  }

  /**
   * 保存错题本到本地存储
   */
  saveErrorBook() {
    try {
      // 使用独立的存储键
      uni.setStorageSync(`${this.storageKeyPrefix}chinese`, this.errorWords.chinese);
      uni.setStorageSync(`${this.storageKeyPrefix}english`, this.errorWords.english);

      console.log('错题本保存数据:', {
        chinese: {
          count: this.errorWords.chinese.length,
          words: this.errorWords.chinese
        },
        english: {
          count: this.errorWords.english.length,
          words: this.errorWords.english
        }
      });

      return true;
    } catch (e) {
      console.error('保存错题本失败:', e);
      return false;
    }
  }

  /**
   * 添加词语到错题本
   * @param {string} word - 要添加的词语
   * @returns {Object} - 添加结果
   */
  addToErrorBook(word) {
    // 判断当前词语是否为英文
    const isEnglish = /^[a-zA-Z\\s]+$/.test(word);
    const type = isEnglish ? 'english' : 'chinese';

    const wordExists = this.errorWords[type].find(w => w.word === word);

    if (!wordExists) {
      const newErrorWord = {
        word: word,
        correctCount: 0,
        maxCorrectNeeded: this.maxCorrectNeeded,
        addTime: Date.now(),
        lastDictationId: null
      };

      this.errorWords[type].push(newErrorWord);
      this.saveErrorBook();

      return {
        success: true,
        message: `已添加到${isEnglish ? '英文' : '中文'}错题本`,
        type: type
      };
    }
    //打印储存的错题
    console.log('错题本数据:', this.errorWords);
    return {
      success: false,
      message: '词语已存在于错题本中',
      type: type
    };
  }

  /**
   * 从错题本移除词语
   * @param {string} word - 要移除的词语
   * @param {string} type - 类型（chinese/english），如果不提供则自动判断
   * @returns {boolean} - 是否成功移除
   */
  removeFromErrorBook(word, type) {
    if (!type) {
      // 如果未提供类型，自动判断
      type = /^[a-zA-Z\\s]+$/.test(word) ? 'english' : 'chinese';
    }

    const initialLength = this.errorWords[type].length;
    this.errorWords[type] = this.errorWords[type].filter(w => w.word !== word);

    // 如果长度变化，说明成功移除了
    const removed = initialLength > this.errorWords[type].length;

    if (removed) {
      this.saveErrorBook();
    }
    //打印储存的错题
    console.log('错题本数据:', this.errorWords);
    return removed;
  }

  /**
   * 获取词语的正确次数
   * @param {string} word - 要查询的词语
   * @returns {number} - 正确次数
   */
  getCorrectCount(word) {
    const type = /^[a-zA-Z\\s]+$/.test(word) ? 'english' : 'chinese';
    const errorWord = this.errorWords[type].find(ew => ew.word === word);
    return errorWord ? errorWord.correctCount : 0;
  }

  /**
   * 标记词语为正确
   * @param {string} word - 要标记的词语
   * @param {string} dictationId - 当前听写的ID
   * @returns {Object} - 标记结果
   */
  markAsCorrect(word, dictationId) {
    const type = /^[a-zA-Z\\s]+$/.test(word) ? 'english' : 'chinese';
    const errorWord = this.errorWords[type].find(ew => ew.word === word);

    if (!errorWord) {
      return {
        success: false,
        message: '未找到错题记录',
        count: 0
      };
    }

    if (errorWord.correctCount > 0 && errorWord.lastDictationId === dictationId) {
      // 如果在当前听写中已经标记过正确，则取消标记
      errorWord.correctCount = Math.max(0, errorWord.correctCount - 1);
      errorWord.lastDictationId = null;

      this.saveErrorBook();

      return {
        success: true,
        message: '已取消正确标记',
        count: errorWord.correctCount
      };
    } else {
      // 如果在当前听写中还未标记正确，则添加标记
      errorWord.correctCount++;
      errorWord.lastDictationId = dictationId;

      const message = `已添加正确标记(${errorWord.correctCount}/${errorWord.maxCorrectNeeded})`;

      // 如果达到最大正确次数，添加提示，但不立即移除
      // 下次加载页面时才会移除，以便用户可以取消最后一次的“正确”操作
      if (errorWord.correctCount >= errorWord.maxCorrectNeeded) {
        this.saveErrorBook();

        return {
          success: true,
          message: '已达到3次正确，下次进入时将从错题本移除',
          count: errorWord.correctCount,
          completed: true
        };
      }

      this.saveErrorBook();

      return {
        success: true,
        message: message,
        count: errorWord.correctCount
      };
    }
  }

  /**
   * 切换正确标记
   * @param {string} word - 要切换的词语
   * @param {string} dictationId - 当前听写的ID
   * @returns {Object} - 切换结果
   */
  toggleCorrect(word, dictationId) {
    // 如果词语不在错题本中，先添加到错题本
    if (!this.isInErrorBook(word)) {
      this.addToErrorBook(word);
    }

    // 调用 markAsCorrect 方法标记正确
    const result = this.markAsCorrect(word, dictationId);

    // 返回结果，包含正确次数和最大正确次数
    const type = /^[a-zA-Z\\s]+$/.test(word) ? 'english' : 'chinese';
    const errorWord = this.errorWords[type].find(ew => ew.word === word);

    if (errorWord) {
      return {
        ...result,
        correctCount: errorWord.correctCount,
        maxCorrectNeeded: errorWord.maxCorrectNeeded,
        lastDictationId: errorWord.lastDictationId
      };
    }

    return result;
  }

  /**
   * 检查词语是否在错题本中
   * @param {string} word - 要检查的词语
   * @returns {boolean} - 是否在错题本中
   */
  isInErrorBook(word) {
    const type = /^[a-zA-Z\\s]+$/.test(word) ? 'english' : 'chinese';
    console.log('是否在错题本中',this.errorWords[type].some(ew => ew.word === word));
    return this.errorWords[type].some(ew => ew.word === word);
  }

  /**
   * 获取指定类型的错题列表
   * @param {string} type - 类型（chinese/english）
   * @returns {Array} - 错题列表
   */
  getErrorWords(type) {
    return [...(this.errorWords[type] || [])];
  }

  /**
   * 切换错题标记
   * @param {string} word - 要切换的词语
   * @returns {Object} - 操作结果
   */
  toggleErrorMark(word) {
    if (this.isInErrorBook(word)) {
      const removed = this.removeFromErrorBook(word);
      return {
        success: removed,
        message: removed ? '已从错题本移除' : '移除失败',
        inErrorBook: false
      };
    } else {
      const result = this.addToErrorBook(word);
      return {
        ...result,
        inErrorBook: true
      };
    }
  }
}

// 创建单例实例
const errorBookManager = new ErrorBookManager();

export default errorBookManager;
