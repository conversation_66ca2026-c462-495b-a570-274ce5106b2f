/**
 * 腾讯云翻译工具
 * 使用腾讯云机器翻译 API 进行翻译
 *
 * 使用TC3-HMAC-SHA256签名算法
 */
// 腾讯云的 SecretId 和 SecretKey
const SECRET_ID = 'AKIDNdgW8XjhRQQEpFfx4yf8RYWKvITREqHZ'; // 替换为您的腾讯云 SecretId
const SECRET_KEY = 'DIG3d0EJRTK2EF5PgpmZBZmmbDcuzqVz'; // 替换为您的腾讯云 SecretKey
// 腾讯云翻译 API 的地域
const REGION = 'ap-beijing'; // 使用北京地区的接口
// 腾讯云翻译 API 的服务名
const SERVICE = 'tmt';
// 接口版本
const VERSION = '2018-03-21';
// 接口动作，这里以文本翻译为例
const ACTION = 'TextTranslate';
// 请求方法
const METHOD = 'POST';
// 接口地址
const ENDPOINT = 'tmt.tencentcloudapi.com';

// 不再使用签名缓存，确保每次请求都生成新签名

// 按需引入crypto-js库的特定模块，减小打包体积
import SHA256 from 'crypto-js/sha256';
import HmacSHA256 from 'crypto-js/hmac-sha256';
import Hex from 'crypto-js/enc-hex';

/**
 * SHA256哈希函数
 * @param {string} message - 要哈希的消息
 * @param {string} secret - 密钥
 * @param {string} encoding - 编码方式
 * @returns {string} - 哈希结果
 */
function sha256(message, secret = '', encoding) {
    if (secret) {
        // 使用密钥进行HMAC-SHA256哈希
        const hmac = HmacSHA256(message, secret);
        return encoding === 'hex' ? hmac.toString(Hex) : hmac;
    } else {
        // 普通SHA256哈希
        const hash = SHA256(message);
        return encoding === 'hex' ? hash.toString(Hex) : hash;
    }
}

/**
 * 获取哈希值
 * @param {string} message - 要哈希的消息
 * @returns {string} - 哈希结果，十六进制格式
 */
function getHash(message) {
    const hash = SHA256(message);
    return hash.toString(Hex);
}

/**
 * 获取日期字符串
 * @param {number} timestamp - 时间戳
 * @returns {string} - 日期字符串，格式为YYYY-MM-DD
 */
function getDate(timestamp) {
    const date = new Date(timestamp * 1000);
    const year = date.getUTCFullYear();
    const month = ('0' + (date.getUTCMonth() + 1)).slice(-2);
    const day = ('0' + date.getUTCDate()).slice(-2);
    return `${year}-${month}-${day}`;
}

/**
 * 生成TC3-HMAC-SHA256签名
 * @param {Object} params - 请求参数
 * @returns {Object} - 包含签名和请求头的对象
 */
function generateTC3Signature(params) {
    try {
        // 获取当前时间戳
        const timestamp = Math.floor(Date.now() / 1000);
        console.log('生成签名，时间戳:', timestamp);

        // 获取日期
        const date = getDate(timestamp);
        console.log('日期:', date);

        // 步骤1：拼接规范请求串
        const payload = JSON.stringify(params);
        console.log('请求参数:', payload);
        const hashedRequestPayload = getHash(payload);
        console.log('哈希后的请求参数:', hashedRequestPayload);

        const canonicalHeaders = "content-type:application/json; charset=utf-8\n" +
                               "host:" + ENDPOINT + "\n" +
                               "x-tc-action:" + ACTION.toLowerCase() + "\n";
        const signedHeaders = "content-type;host;x-tc-action";

        const canonicalRequest = METHOD + "\n" +
                               "/" + "\n" +
                               "" + "\n" +
                               canonicalHeaders + "\n" +
                               signedHeaders + "\n" +
                               hashedRequestPayload;
        console.log('规范请求串:', canonicalRequest);

        // 步骤2：拼接待签名字符串
        const algorithm = "TC3-HMAC-SHA256";
        const hashedCanonicalRequest = getHash(canonicalRequest);
        console.log('哈希后的规范请求串:', hashedCanonicalRequest);
        const credentialScope = date + "/" + SERVICE + "/" + "tc3_request";
        console.log('凭证范围:', credentialScope);
        const stringToSign = algorithm + "\n" +
                           timestamp + "\n" +
                           credentialScope + "\n" +
                           hashedCanonicalRequest;
        console.log('待签名字符串:', stringToSign);

        // 步骤3：计算签名
        const kDate = sha256(date, 'TC3' + SECRET_KEY);
        const kService = sha256(SERVICE, kDate);
        const kSigning = sha256('tc3_request', kService);
        const signature = sha256(stringToSign, kSigning, 'hex');
        console.log('签名:', signature);

        // 步骤4：拼接Authorization
        const authorization = algorithm + " " +
                            "Credential=" + SECRET_ID + "/" + credentialScope + ", " +
                            "SignedHeaders=" + signedHeaders + ", " +
                            "Signature=" + signature;
        console.log('Authorization:', authorization);

        // 不再缓存签名，确保每次请求都生成新签名
        return {
            authorization: authorization,
            timestamp: timestamp
        };
    } catch (error) {
        console.error('生成签名失败:', error);
        throw error;
    }
}

/**
 * 翻译文本
 * @param {string} text - 要翻译的文本
 * @param {string} source - 源语言，如 'en', 'zh'
 * @param {string} target - 目标语言，如 'en', 'zh'
 * @param {number} projectId - 项目ID，默认为0
 * @returns {Promise<Object>} - 翻译结果
 */
export const translateText = async (text, source = 'en', target = 'zh', projectId = 0) => {
    try {
        console.log('【API请求】开始翻译文本:', text);
        console.log('【API请求】调用栈:', new Error().stack);

        if (!text) {
            console.error('文本不能为空');
            return Promise.reject(new Error('文本不能为空'));
        }

        // 验证语言参数
        const validLanguages = ['zh', 'en', 'ja', 'ko', 'fr', 'es', 'it', 'de', 'tr', 'ru', 'pt', 'vi', 'id', 'th', 'ms'];
        const sourceUpper = source.toLowerCase();
        const targetUpper = target.toLowerCase();

        console.log('【API请求】翻译方向:', sourceUpper, '->', targetUpper);

        if (!validLanguages.includes(sourceUpper)) {
            console.error('源语言无效:', sourceUpper);
            return Promise.reject(new Error(`源语言无效，支持的语言: ${validLanguages.join(', ')}`));
        }

        if (!validLanguages.includes(targetUpper)) {
            console.error('目标语言无效:', targetUpper);
            return Promise.reject(new Error(`目标语言无效，支持的语言: ${validLanguages.join(', ')}`));
        }

        // 准备请求参数
        const params = {
            SourceText: text,
            Source: sourceUpper,
            Target: targetUpper,
            ProjectId: projectId
        };

        console.log('【API请求】请求参数:', params);

        // 生成签名
        console.log('【API请求】开始生成签名...');
        const { authorization, timestamp } = generateTC3Signature(params);
        console.log('【API请求】签名生成完成');

        // 构建请求头
        const headers = {
            'Content-Type': 'application/json; charset=utf-8',
            'Authorization': authorization,
            'X-TC-Timestamp': timestamp.toString(),
            'X-TC-Action': ACTION,
            'X-TC-Version': VERSION,
            'X-TC-Region': REGION
        };

        console.log('【API请求】请求头:', headers);
        console.log('【API请求】请求URL:', `https://${ENDPOINT}`);

        // 发送请求
        return new Promise((resolve, reject) => {
            console.log('【API请求】发送请求...');
            uni.request({
                url: `https://${ENDPOINT}`,
                method: METHOD,
                data: params,
                header: headers,
                success: (res) => {
                    console.log('【API请求】收到响应:', res);

                    if (res.statusCode === 200) {
                        const data = res.data;
                        console.log('【API请求】响应数据:', data);

                        if (data.Response && data.Response.TargetText) {
                            console.log('【API请求】翻译成功:', data.Response.TargetText);
                            resolve({
                                src: text,
                                dst: data.Response.TargetText,
                                from: sourceUpper,
                                to: targetUpper
                            });
                        } else if (data.Response && data.Response.Error) {
                            console.error('【API请求】API 错误:', data.Response.Error);
                            reject(new Error(`API 错误: ${data.Response.Error.Message || '未知错误'}`));
                        } else {
                            console.error('【API请求】翻译结果格式错误:', data);
                            reject(new Error('翻译结果格式错误'));
                        }
                    } else {
                        console.error('【API请求】请求失败，状态码:', res.statusCode, '响应:', res.data);
                        reject(new Error(`请求失败，状态码: ${res.statusCode}`));
                    }
                },
                fail: (err) => {
                    console.error('【API请求】请求失败:', err);
                    reject(new Error(`请求失败: ${err.errMsg || JSON.stringify(err)}`));
                }
            });
        });
    } catch (error) {
        console.error('【API请求】文本翻译失败:', error);
        return Promise.reject(error);
    }
};

/**
 * 翻译单词列表
 * @param {Array} words - 要翻译的单词列表
 * @param {string} direction - 翻译方向，'en2zh'（英文到中文）或 'zh2en'（中文到英文）
 * @returns {Promise<Array>} - 翻译结果数组
 */
export const translateWords = async (words, direction = 'en2zh') => {
    try {
        if (!words || !Array.isArray(words) || words.length === 0) {
            return Promise.reject(new Error('单词列表不能为空'));
        }

        // 验证翻译方向
        if (direction !== 'en2zh' && direction !== 'zh2en') {
            return Promise.reject(new Error('翻译方向无效，必须是 "en2zh" 或 "zh2en"'));
        }

        // 设置源语言和目标语言
        const source = direction === 'en2zh' ? 'en' : 'zh';
        const target = direction === 'en2zh' ? 'zh' : 'en';

        // 将单词列表合并为一个字符串，用逗号分隔
        const text = words.join(',');

        // 调用翻译API
        const result = await translateText(text, source, target);

        // 将翻译结果拆分为数组
        let translations = [];
        if (result && result.dst) {
            // 根据目标语言选择分隔符
            if (target === 'zh') {
                // 中文结果可能使用中文逗号、顿号、或英文逗号
                translations = result.dst.split(/[,，、]/).map(t => t.trim()).filter(t => t);
            } else {
                // 英文结果使用英文逗号或中文逗号
                translations = result.dst.split(/[,，]/).map(t => t.trim()).filter(t => t);
            }

            console.log('翻译结果拆分:', {
                originalText: result.dst,
                splitResult: translations,
                wordsCount: words.length,
                translationsCount: translations.length
            });
        } else {
            console.warn('翻译结果为空，使用原始单词');
            return words;
        }

        // 确保翻译结果和单词数量一致
        if (translations.length !== words.length) {
            console.warn('翻译结果数量与单词数量不一致:', {
                words: words,
                translations: translations,
                originalResult: result.dst
            });

            // 如果翻译结果数量少于单词数量，补充原始单词
            if (translations.length < words.length) {
                console.log('翻译结果数量不足，补充原始单词');
                for (let i = translations.length; i < words.length; i++) {
                    translations.push(words[i]);
                }
            }
            // 如果翻译结果数量多于单词数量，截取前面部分
            else if (translations.length > words.length) {
                console.log('翻译结果数量过多，截取前面部分');
                translations = translations.slice(0, words.length);
            }
        }

        console.log('最终翻译结果:', {
            words: words,
            translations: translations
        });

        return translations;
    } catch (error) {
        console.error('单词列表翻译失败:', error);
        // 如果翻译失败，返回原始单词列表
        return words;
    }
};

/**
 * 翻译单个单词
 * @param {string} word - 要翻译的单词
 * @param {string} direction - 翻译方向，'en2zh'（英文到中文）或 'zh2en'（中文到英文）
 * @returns {Promise<string>} - 翻译结果
 */
export const translateWord = async (word, direction = 'en2zh') => {
    try {
        if (!word || typeof word !== 'string') {
            return Promise.reject(new Error('单词不能为空'));
        }

        // 检查输入是否包含逗号，如果包含，说明可能是多个单词
        if (word.includes(',') || word.includes('，')) {
            console.log('检测到逗号分隔的多个单词，作为单词列表处理');
            // 将输入按逗号分割，并去除空白
            const wordList = word.split(/[,，]/).map(w => w.trim()).filter(w => w);

            // 如果确实是多个单词，直接调用translateWords
            if (wordList.length > 1) {
                const translations = await translateWords(wordList, direction);
                // 返回与原格式相同的结果（用逗号连接）
                return translations.join(', ');
            }
            // 如果只有一个单词（可能末尾有逗号），继续处理单个单词
        }

        // 处理单个单词
        const results = await translateWords([word], direction);
        return results[0] || word;
    } catch (error) {
        console.error('单词翻译失败:', error);
        // 如果翻译失败，返回原单词
        return word;
    }
};

/**
 * 检测语言类型
 * @param {string} text - 要检测的文本
 * @returns {string} - 返回语言类型，'en'（英文）或 'zh'（中文）
 */
export const detectLanguage = (text) => {
    if (!text) return '';

    // 简单的语言检测：如果包含中文字符，则认为是中文
    const zhPattern = /[\u4e00-\u9fa5]/;
    return zhPattern.test(text) ? 'zh' : 'en';
};

/**
 * 智能翻译（自动检测语言并翻译）
 * @param {string} text - 要翻译的文本
 * @returns {Promise<string>} - 翻译结果
 */
export const smartTranslate = async (text) => {
    try {
        if (!text) {
            return Promise.reject(new Error('文本不能为空'));
        }

        // 检查是否是逗号分隔的单词列表
        if (text.includes(',') || text.includes('，')) {
            console.log('智能翻译检测到逗号分隔的文本');
            // 检测第一个单词的语言来确定整个列表的语言
            const firstWord = text.split(/[,，]/)[0].trim();
            if (firstWord) {
                const sourceLanguage = detectLanguage(firstWord);
                const direction = sourceLanguage === 'zh' ? 'zh2en' : 'en2zh';

                // 使用translateWord处理，它已经能处理逗号分隔的列表
                return await translateWord(text, direction);
            }
        }

        // 常规文本处理
        const sourceLanguage = detectLanguage(text);
        const targetLanguage = sourceLanguage === 'zh' ? 'en' : 'zh';

        const result = await translateText(text, sourceLanguage, targetLanguage);
        return result && result.dst ? result.dst : text;
    } catch (error) {
        console.error('智能翻译失败:', error);
        // 如果翻译失败，返回原文
        return text;
    }
};

export default {
    translateText,
    translateWords,
    translateWord,
    detectLanguage,
    smartTranslate
};
