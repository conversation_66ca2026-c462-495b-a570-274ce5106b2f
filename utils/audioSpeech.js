/**
 * 通用语音朗读工具类
 * 支持中英文自动识别，多平台兼容
 */
class AudioSpeech {
  constructor() {
    // 初始化状态
    this.audioContext = null;
    this.retryCount = 0;
    this.maxRetries = 2;
    this.isPlaying = false;
    this.pendingPlay = false;
    this.isApp = this.checkIsApp();
    this.TTS = null;
    this.baiduTTSKey = 'tiOawXWQDx8X3THyy1ppptUD';
    this.baiduTTSSecret = '05HbTZxiGH4N3tc2XWvOFir15nt9pEqd';
    this.baiduToken = '';
    this.tokenExpireTime = 0;
    // 添加音频缓存
    this.audioCache = new Map();
    // 缓存过期时间（24小时）
    this.cacheDuration = 24 * 60 * 60 * 1000;
    
    // 初始化APP环境的TTS
    if (this.isApp) {
      try {
        // #ifdef APP-PLUS
        const { CreateTTSSpeaker } = require('@/uni_modules/xwq-tts-speaker/js_sdk/index.js');
        this.TTS = CreateTTSSpeaker();
        // #endif
      } catch (e) {
        console.error('TTS初始化失败:', e);
      }
    }
    
    // #ifdef MP-WEIXIN
    // 获取当前页面实例
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    
    // 初始化微信小程序环境
    this.initWxEnvironment();
    
    // 监听页面隐藏
    if (currentPage) {
      const originalOnHide = currentPage.onHide || function() {};
      currentPage.onHide = () => {
        originalOnHide.call(currentPage);
        this.stop();
      };
      
      const originalOnUnload = currentPage.onUnload || function() {};
      currentPage.onUnload = () => {
        originalOnUnload.call(currentPage);
        this.stop();
      };
    }
    // #endif
    
    // #ifdef H5
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.stop();
      }
    });
    // #endif

    // 初始化时从本地存储加载token
    this.loadTokenFromStorage();
    // 初始化时从本地存储加载缓存
    this.loadAudioCache();
  }

  // 初始化微信小程序环境
  async initWxEnvironment() {
    // #ifdef MP-WEIXIN
    try {
      // 检查微信版本
      const systemInfo = uni.getSystemInfoSync();
      if (systemInfo.SDKVersion && systemInfo.SDKVersion < '2.10.0') {
        console.warn('当前微信版本过低，无法使用语音合成功能');
        return;
      }

      // 检查权限
      const setting = await new Promise((resolve, reject) => {
        wx.getSetting({
          success: (res) => resolve(res.authSetting),
          fail: reject
        });
      });

      // 如果没有录音权限，请求权限
      if (!setting['scope.record']) {
        await new Promise((resolve, reject) => {
          wx.authorize({
            scope: 'scope.record',
            success: resolve,
            fail: reject
          });
        });
      }

      // 初始化日志目录
      await this.initLogDirectory();

    } catch (error) {
      console.error('初始化微信环境失败:', error);
    }
    // #endif
  }

  // 初始化日志目录
  async initLogDirectory() {
    // #ifdef MP-WEIXIN
    console.log('已授权');
    try {
      const fs = wx.getFileSystemManager();
      const userDataPath = wx.env.USER_DATA_PATH;
      const logDir = `${userDataPath}/miniprogramLog`;
      
      // 检查目录是否存在
      try {
        fs.accessSync(logDir);
      } catch (e) {
        // 目录不存在，创建目录
        fs.mkdirSync(logDir, true);
      }

      // 创建日志文件
      const logFile = `${logDir}/log2`;
      try {
        fs.accessSync(logFile);
      } catch (e) {
        // 文件不存在，创建文件
        fs.writeFileSync(logFile, '', 'utf8');
      }

      // 创建隐私目录
      const privacyDir = `${userDataPath}/__wxprivate__/privacy`;
      try {
        fs.accessSync(privacyDir);
      } catch (e) {
        // 目录不存在，创建目录
        fs.mkdirSync(privacyDir, true);
      }

      // 创建权限状态文件
      const scopeStateFile = `${privacyDir}/scopeState.txt`;
      try {
        fs.accessSync(scopeStateFile);
      } catch (e) {
        // 文件不存在，创建文件
        fs.writeFileSync(scopeStateFile, JSON.stringify({
          'scope.record': true,
          'scope.userLocation': true
        }), 'utf8');
      }

    } catch (error) {
      console.error('初始化日志目录失败:', error);
    }
    // #endif
  }

  // 检查是否是APP环境
  checkIsApp() {
    // #ifdef APP-PLUS
    return true;
    // #endif
    return false;
  }

  /**
   * 判断文本是否为英文
   * @param {string} str - 要判断的文本
   * @returns {boolean} - true为英文，false为中文
   */
  isEnglishOrPunctuationByCode(str) {
    for (let i = 0; i < str.length; i++) {
      const charCode = str.charCodeAt(i);
      // 中文字符的 Unicode 编码范围是 \u4e00 - \u9fa5
      if (charCode >= 0x4e00 && charCode <= 0x9fa5) {
        return false;
      }
    }
    return true;
  }

  /**
   * 统一的语音播放接口
   * @param {Object} options - 配置选项
   * @param {string} options.text - 要朗读的文本
   * @param {string} [options.languageType] - 语言类型，可选 'en'/'zh'，不传则自动识别
   * @returns {Promise} - 返回播放完成的Promise
   */
  speak(options) {
    const text = options.text;
    if (!text) {
      return Promise.reject(new Error('Text is required'));
    }

    // 确保在开始新的播放前清理旧的资源
    this.stop();

    // 延迟一小段时间再开始新的播放
    return new Promise((resolve) => {
      setTimeout(() => {
        this.speakInternal(options).then(resolve).catch(() => {
          resolve(); // 即使失败也继续执行
        });
      }, 100);
    });
  }

  /**
   * 内部播放实现
   * @private
   */
  speakInternal(options) {
    const text = options.text;
    const isEnglish = options.languageType ? 
      options.languageType === 'en' : 
      this.isEnglishOrPunctuationByCode(text);

    // 如果是中文且不是H5环境，优先使用百度语音合成
    // #ifndef H5
    if (!isEnglish) {
      return new Promise((resolve) => {
        this.playWithBaiduTTS(text, resolve);
      });
    }
    // #endif

    // 微信小程序环境使用微信语音合成
    // #ifdef MP-WEIXIN
    if (!isEnglish) {
      return new Promise((resolve) => {
        try {
          // 检查微信版本是否支持语音合成
          const systemInfo = uni.getSystemInfoSync();
          if (systemInfo.SDKVersion && systemInfo.SDKVersion < '2.10.0') {
            console.warn('当前微信版本过低，无法使用语音合成功能');
            // 使用备用方案
            this.playAudio(text, isEnglish).then(resolve).catch(() => resolve());
            return;
          }

          // 确保 wx 对象存在且 speechSynthesis 方法可用
          if (typeof wx !== 'undefined' && wx.speechSynthesis) {
            // 确保日志目录存在
            this.initLogDirectory().then(() => {
             
              wx.speechSynthesis({
                lang: 'zh_CN',
                text: text,
                success: () => {
                  console.log('语音播报成功');
                  this.isPlaying = true;
                  // 由于微信小程序没有播放结束的回调，我们设置一个延时
                  setTimeout(() => {
                    this.isPlaying = false;
                    resolve();
                  }, text.length * 500); // 根据文本长度估算播放时间
                },
                fail: (err) => {
                  console.error('语音播报失败:', err);
                  this.isPlaying = false;
                  // 失败时使用备用方案
                  this.playAudio(text, isEnglish).then(resolve).catch(() => resolve());
                }
              });
            }).catch(() => {
              // 如果目录初始化失败，使用备用方案
              this.playAudio(text, isEnglish).then(resolve).catch(() => resolve());
            });
          } else {
            console.warn('当前环境不支持微信语音合成，使用备用方案');
            this.playAudio(text, isEnglish).then(resolve).catch(() => resolve());
          }
        } catch (error) {
          console.error('微信语音合成错误:', error);
          this.isPlaying = false;
          this.playAudio(text, isEnglish).then(resolve).catch(() => resolve());
        }
      });
    }
    // #endif

    // H5环境使用Web Speech API
    // #ifdef H5
    if (typeof window !== 'undefined' && window.speechSynthesis && !isEnglish) {
      return new Promise((resolve) => {
        try {
          const utterance = new SpeechSynthesisUtterance(text);
          utterance.lang = 'zh-CN';
          utterance.rate = 0.8;
          utterance.pitch = 1;
          utterance.volume = 1;

          utterance.onend = () => {
            this.isPlaying = false;
            resolve();
          };

          utterance.onerror = () => {
            this.isPlaying = false;
            this.playAudio(text, isEnglish).then(resolve).catch(() => resolve());
          };

          this.isPlaying = true;
          window.speechSynthesis.speak(utterance);
        } catch (error) {
          this.isPlaying = false;
          this.playAudio(text, isEnglish).then(resolve).catch(() => resolve());
        }
      });
    }
    // #endif

    return this.playAudio(text, isEnglish);
  }

  /**
   * 播放音频
   * @private
   */
  playAudio(text, isEnglish) {
    return new Promise((resolve) => {
      try {
        // 确保清理旧的音频上下文
        this.cleanupAudioContext();
        
        // 创建新的音频上下文
        this.audioContext = uni.createInnerAudioContext();
        
        // 设置音频源
        const audioUrl = this.getAudioUrl(text, isEnglish);
        this.audioContext.src = audioUrl;
        
        // 设置音频属性
        // #ifdef MP-WEIXIN
        this.audioContext.obeyMuteSwitch = false;
        // #endif
        
        // 设置事件监听
        this.setupAudioEvents(this.audioContext, text, isEnglish, resolve);
        
        // 微信小程序环境特殊处理
        // #ifdef MP-WEIXIN
        this.audioContext.onCanplay(() => {
          // 确保音频已经准备好再播放
          setTimeout(() => {
            if (this.audioContext && !this.isPlaying) {
              this.isPlaying = true;
              this.audioContext.play();
            }
          }, 50);
        });
        // #endif
        
        // 非微信小程序环境直接播放
        // #ifndef MP-WEIXIN
        this.isPlaying = true;
        this.audioContext.play();
        // #endif
        
      } catch (error) {
        console.error('播放音频错误:', error);
        this.cleanupAudioContext();
        resolve();
      }
    });
  }

  /**
   * 获取音频URL
   * @private
   */
  getAudioUrl(text, isEnglish) {
    if (isEnglish) {
      // 英文统一使用有道词典服务
      return `https://dict.youdao.com/dictvoice?audio=${encodeURIComponent(text)}&type=2`;
    } else {
      // 中文使用百度TTS服务
      return `https://tts.baidu.com/text2audio?lan=zh&ie=UTF-8&spd=5&text=${encodeURIComponent(text)}`;
    }
  }

  /**
   * 从本地存储加载token
   * @private
   */
  loadTokenFromStorage() {
    try {
      const tokenInfo = uni.getStorageSync('baiduTTSToken');
      if (tokenInfo) {
        const { token, expireTime } = JSON.parse(tokenInfo);
        const now = Date.now();
        
        // 如果token未过期且距离过期还有超过1天，则使用缓存的token
        if (token && expireTime && expireTime > now + 24 * 60 * 60 * 1000) {
          this.baiduToken = token;
          this.tokenExpireTime = expireTime;
          console.log('从本地存储加载token成功');
        } else {
          console.log('缓存的token已过期或即将过期');
          this.refreshToken();
        }
      }
    } catch (error) {
      console.error('加载token失败:', error);
    }
  }

  /**
   * 保存token到本地存储
   * @private
   */
  saveTokenToStorage() {
    try {
      const tokenInfo = JSON.stringify({
        token: this.baiduToken,
        expireTime: this.tokenExpireTime
      });
      uni.setStorageSync('baiduTTSToken', tokenInfo);
      console.log('token保存成功');
    } catch (error) {
      console.error('保存token失败:', error);
    }
  }

  /**
   * 刷新token
   * @private
   */
  async refreshToken() {
    try {
      const response = await uni.request({
        url: `https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=${this.baiduTTSKey}&client_secret=${this.baiduTTSSecret}`,
        method: 'GET'
      });

      if (response && response.data && response.data.access_token) {
        this.baiduToken = response.data.access_token;
        // token有效期默认30天，这里设置为29天以确保安全边际
        this.tokenExpireTime = Date.now() + 29 * 24 * 60 * 60 * 1000;
        
        // 保存到本地存储
        this.saveTokenToStorage();
        
        return this.baiduToken;
      } else {
        throw new Error('获取百度token失败');
      }
    } catch (error) {
      console.error('刷新token失败:', error);
      throw error;
    }
  }

  /**
   * 获取百度访问令牌
   * @private
   */
  async getBaiduToken() {
    try {
      const now = Date.now();
      
      // 如果token不存在或即将过期（小于1天），则刷新token
      if (!this.baiduToken || this.tokenExpireTime <= now + 24 * 60 * 60 * 1000) {
        console.log('token不存在或即将过期，开始刷新');
        return await this.refreshToken();
      }
      
      return this.baiduToken;
    } catch (error) {
      console.error('获取token失败:', error);
      throw error;
    }
  }

  /**
   * 从本地存储加载音频缓存
   * @private
   */
  loadAudioCache() {
    try {
      const cacheData = uni.getStorageSync('baiduTTSCache');
      if (cacheData) {
        const { cache, timestamp } = JSON.parse(cacheData);
        // 检查缓存是否过期
        if (Date.now() - timestamp < this.cacheDuration) {
          this.audioCache = new Map(cache);
          console.log('已从本地存储加载音频缓存');
        } else {
          // 缓存过期，清除缓存
          uni.removeStorageSync('baiduTTSCache');
          console.log('音频缓存已过期，已清除');
        }
      }
    } catch (error) {
      console.error('加载音频缓存失败:', error);
    }
  }

  /**
   * 保存音频缓存到本地存储
   * @private
   */
  saveAudioCache() {
    try {
      const cacheData = {
        cache: Array.from(this.audioCache.entries()),
        timestamp: Date.now()
      };
      uni.setStorageSync('baiduTTSCache', JSON.stringify(cacheData));
    } catch (error) {
      console.error('保存音频缓存失败:', error);
    }
  }

  /**
   * 获取缓存的音频URL
   * @private
   */
  getCachedAudioUrl(text) {
    const cacheKey = text;
    const cachedData = this.audioCache.get(cacheKey);
    if (cachedData && Date.now() - cachedData.timestamp < this.cacheDuration) {
      console.log('使用缓存的音频:', text);
      return cachedData.url;
    }
    return null;
  }

  /**
   * 缓存音频URL
   * @private
   */
  cacheAudioUrl(text, url) {
    const cacheKey = text;
    this.audioCache.set(cacheKey, {
      url: url,
      timestamp: Date.now()
    });
    // 保存到本地存储
    this.saveAudioCache();
  }

  /**
   * 使用百度语音合成播放
   * @private
   */
  async playWithBaiduTTS(text, resolve) {
    try {
      
      
      // 检查缓存
      const cachedUrl = this.getCachedAudioUrl(text);
      if (cachedUrl) {
        return this.playWithNewContext(cachedUrl).then(resolve);
      }
      console.log('baidu请求：', text);

      // 获取token
      const token = await this.getBaiduToken();
      
      // 创建新的音频上下文
      const baiduContext = uni.createInnerAudioContext();
      
      // 设置百度语音合成参数
      const params = {
        tex: encodeURIComponent(text),
        tok: token,
        cuid: 'uniapp_tts',
        ctp: 1,
        lan: 'zh',
        spd: 5,
        pit: 5,
        vol: 5,
        per: 1, // 修改为男声(度逍遥)
        aue: 3
      };
      
      // 构建URL
      const url = 'https://tsn.baidu.com/text2audio?' + Object.keys(params)
        .map(key => `${key}=${params[key]}`)
        .join('&');
      
      // 缓存URL
      this.cacheAudioUrl(text, url);
      
      // 设置音频源
      baiduContext.src = url;
      
      // #ifdef MP-WEIXIN
      baiduContext.obeyMuteSwitch = false;
      // #endif

      // 设置事件监听
      baiduContext.onCanplay(() => {
        setTimeout(() => {
          if (!this.isPlaying) {
            this.isPlaying = true;
            baiduContext.play();
          }
        }, 50);
      });

      baiduContext.onPlay(() => {
        console.log('百度语音合成开始播放');
        this.isPlaying = true;
      });

      baiduContext.onError((err) => {
        console.error('百度语音合成播放失败:', err);
        this.isPlaying = false;
        baiduContext.destroy();
        // 播放失败时从缓存中移除
        this.audioCache.delete(text);
        this.saveAudioCache();
        resolve();
      });

      baiduContext.onEnded(() => {
        console.log('百度语音合成播放结束');
        this.isPlaying = false;
        baiduContext.destroy();
        resolve();
      });

      baiduContext.onStop(() => {
        console.log('百度语音合成播放停止');
        this.isPlaying = false;
        baiduContext.destroy();
        resolve();
      });

    } catch (error) {
      console.error('百度语音合成错误:', error);
      this.isPlaying = false;
      resolve();
    }
  }

  /**
   * 设置音频事件监听
   * @private
   */
  setupAudioEvents(audioContext, text, isEnglish, resolve) {
    let hasError = false;

    audioContext.onPlay(() => {
      console.log('开始播放:', text);
      this.isPlaying = true;
    });

    audioContext.onError((err) => {
      console.error('音频播放失败:', text, err);
      hasError = true;
      this.isPlaying = false;
      
      this.cleanupAudioContext();

      if (isEnglish && this.retryCount < this.maxRetries) {
        this.retryCount++;
        console.log('尝试使用备用服务，重试次数:', this.retryCount);
        setTimeout(() => {
          const backupUrl = `https://dict.youdao.com/dictvoice?audio=${encodeURIComponent(text)}&type=1`;
          this.playWithNewContext(backupUrl).then(resolve).catch(() => resolve());
        }, 300);
      } else if (!isEnglish) {
        // 中文播放失败时尝试使用百度语音合成
        console.log('尝试使用百度语音合成');
        this.playWithBaiduTTS(text, resolve);
      } else {
        this.retryCount = 0;
        resolve();
      }
    });

    audioContext.onEnded(() => {
      console.log('播放结束:', text);
      this.isPlaying = false;
      this.cleanupAudioContext();
      this.retryCount = 0;
      resolve();
    });

    audioContext.onStop(() => {
      console.log('播放停止:', text);
      this.isPlaying = false;
      resolve();
    });

    // #ifdef MP-WEIXIN
    audioContext.onWaiting(() => {
      console.log('音频加载中:', text);
    });

    audioContext.onSeeking(() => {
      console.log('音频跳转中:', text);
    });
    // #endif
  }

  /**
   * 使用新的音频上下文播放
   * @private
   */
  playWithNewContext(url) {
    return new Promise((resolve) => {
      try {
        const newContext = uni.createInnerAudioContext();
        newContext.src = url;
        
        // #ifdef MP-WEIXIN
        newContext.obeyMuteSwitch = false;
        // #endif

        newContext.onCanplay(() => {
          setTimeout(() => {
            if (!this.isPlaying) {
              this.isPlaying = true;
              newContext.play();
            }
          }, 50);
        });

        newContext.onPlay(() => {
          console.log('备用服务开始播放');
          this.isPlaying = true;
        });

        newContext.onError(() => {
          console.error('备用服务播放失败');
          this.isPlaying = false;
          newContext.destroy();
          resolve();
        });

        newContext.onEnded(() => {
          console.log('备用服务播放结束');
          this.isPlaying = false;
          newContext.destroy();
          resolve();
        });

        newContext.onStop(() => {
          console.log('备用服务播放停止');
          this.isPlaying = false;
          newContext.destroy();
          resolve();
        });

      } catch (error) {
        console.error('备用服务错误:', error);
        this.isPlaying = false;
        resolve();
      }
    });
  }

  /**
   * 清理音频上下文
   * @private
   */
  cleanupAudioContext() {
    if (this.audioContext) {
      try {
        this.audioContext.stop();
        this.audioContext.destroy();
      } catch (e) {
        console.error('清理音频上下文错误:', e);
      }
      this.audioContext = null;
    }
    this.isPlaying = false;
    this.pendingPlay = false;
  }

  /**
   * 停止播放
   */
  stop() {
    this.cleanupAudioContext();

    // 停止H5 TTS
    // #ifdef H5
    if (typeof window !== 'undefined' && window.speechSynthesis) {
      window.speechSynthesis.cancel();
    }
    // #endif
  }
}

// 导出单例
export const audioSpeech = new AudioSpeech(); 