/**
 * 翻译服务整合工具
 * 整合translator.js和TXtranslator.js，提供容错机制
 * 先使用translator.js获取翻译，如果不成功再使用TXtranslator.js
 * 自动将翻译结果保存到本地存储
 */

import primaryTranslator from '@/utils/translator.js'; // 主要翻译服务
import backupTranslator from '@/utils/TXtranslator.js'; // 备用翻译服务

// 本地存储键名
const STORAGE_KEY = 'translation_cache';
const STORAGE_EXPIRY_KEY = 'translation_cache_expiry';
const CACHE_EXPIRY_DAYS = 30; // 缓存过期时间（天）

// 内存中的翻译结果缓存
const translationCache = new Map();

/**
 * 从本地存储加载翻译缓存
 */
function loadCacheFromStorage() {
  try {
    const cachedData = uni.getStorageSync(STORAGE_KEY);
    const expiryTime = uni.getStorageSync(STORAGE_EXPIRY_KEY);

    // 检查缓存是否过期
    if (cachedData && expiryTime) {
      const now = Date.now();
      if (now < expiryTime) {
        // 缓存未过期，加载到内存
        const parsedData = JSON.parse(cachedData);
        Object.keys(parsedData).forEach(key => {
          translationCache.set(key, parsedData[key]);
        });
        console.log(`从本地存储加载了 ${translationCache.size} 条翻译缓存`);
      } else {
        // 缓存已过期，清除
        uni.removeStorageSync(STORAGE_KEY);
        uni.removeStorageSync(STORAGE_EXPIRY_KEY);
        console.log('翻译缓存已过期，已清除');
      }
    }
  } catch (error) {
    console.error('加载翻译缓存失败:', error);
  }
}

/**
 * 将翻译缓存保存到本地存储
 */
function saveCacheToStorage() {
  try {
    // 将 Map 转换为对象
    const cacheObject = {};
    translationCache.forEach((value, key) => {
      cacheObject[key] = value;
    });

    // 设置过期时间
    const expiryTime = Date.now() + (CACHE_EXPIRY_DAYS * 24 * 60 * 60 * 1000);

    // 保存到本地存储
    uni.setStorageSync(STORAGE_KEY, JSON.stringify(cacheObject));
    uni.setStorageSync(STORAGE_EXPIRY_KEY, expiryTime);

    console.log(`已将 ${translationCache.size} 条翻译缓存保存到本地存储`);
  } catch (error) {
    console.error('保存翻译缓存失败:', error);
  }
}

// 初始化时加载缓存
loadCacheFromStorage();

/**
 * 将语言代码转换为不同翻译器使用的格式
 * @param {string} lang - 语言代码
 * @param {string} targetFormat - 目标格式 'primary'(translator.js) 或 'backup'(TXtranslator.js)
 * @returns {string} - 转换后的语言代码
 */
function convertLanguageCode(lang, targetFormat) {
  const langLower = lang.toLowerCase();

  if (targetFormat === 'primary') {
    // 转换为translator.js使用的格式（大写）
    return langLower === 'zh' ? 'ZH' : 'EN';
  } else {
    // 转换为TXtranslator.js使用的格式（小写）
    return langLower;
  }
}

/**
 * 翻译文本
 * @param {string} text - 要翻译的文本
 * @param {string} source - 源语言，如 'en', 'zh'
 * @param {string} target - 目标语言，如 'en', 'zh'
 * @returns {Promise<Object>} - 翻译结果
 */
export const translateText = async (text, source = 'en', target = 'zh') => {
  try {
    console.log('开始翻译文本:', text);

    if (!text) {
      return Promise.reject(new Error('文本不能为空'));
    }

    // 检查内存缓存
    const cacheKey = `${text}_${source}_${target}`;
    if (translationCache.has(cacheKey)) {
      console.log('使用内存缓存的翻译结果');
      return translationCache.get(cacheKey);
    }

    // 转换语言代码为primaryTranslator使用的格式
    const primarySource = convertLanguageCode(source, 'primary');
    const primaryTarget = convertLanguageCode(target, 'primary');

    try {
      console.log('尝试使用主要翻译服务...');
      // 尝试使用主要翻译服务
      const result = await primaryTranslator.translateText(text, primarySource, primaryTarget);
      console.log('主要翻译服务成功:', result);

      // 缓存结果
      translationCache.set(cacheKey, result);
      // 保存到本地存储
      saveCacheToStorage();
      return result;
    } catch (primaryError) {
      console.warn('主要翻译服务失败，尝试使用备用翻译服务:', primaryError);

      // 转换语言代码为backupTranslator使用的格式
      const backupSource = convertLanguageCode(source, 'backup');
      const backupTarget = convertLanguageCode(target, 'backup');

      // 尝试使用备用翻译服务
      const backupResult = await backupTranslator.translateText(text, backupSource, backupTarget);
      console.log('备用翻译服务成功:', backupResult);

      // 缓存结果
      translationCache.set(cacheKey, backupResult);
      // 保存到本地存储
      saveCacheToStorage();
      return backupResult;
    }
  } catch (error) {
    console.error('所有翻译服务都失败:', error);
    return Promise.reject(error);
  }
};

/**
 * 翻译单词列表
 * @param {Array} words - 要翻译的单词列表
 * @param {string} direction - 翻译方向，'en2zh'（英文到中文）或 'zh2en'（中文到英文）
 * @returns {Promise<Array>} - 翻译结果数组
 */
export const translateWords = async (words, direction = 'en2zh') => {
  try {
    if (!words || !Array.isArray(words) || words.length === 0) {
      return Promise.reject(new Error('单词列表不能为空'));
    }

    // 验证翻译方向
    if (direction !== 'en2zh' && direction !== 'zh2en') {
      return Promise.reject(new Error('翻译方向无效，必须是 "en2zh" 或 "zh2en"'));
    }

    // 设置源语言和目标语言
    const sourceLanguage = direction === 'en2zh' ? 'en' : 'zh';
    const targetLanguage = direction === 'en2zh' ? 'zh' : 'en';

    // 检查缓存，收集未缓存的单词
    const uncachedWords = [];
    const cachedTranslations = [];
    const wordIndexMap = new Map(); // 记录原始索引

    for (let i = 0; i < words.length; i++) {
      const word = words[i];
      const cacheKey = `${word}_${sourceLanguage}_${targetLanguage}`;

      if (translationCache.has(cacheKey)) {
        // 已缓存的单词
        cachedTranslations[i] = translationCache.get(cacheKey).dst;
      } else {
        // 未缓存的单词
        uncachedWords.push(word);
        wordIndexMap.set(word, i);
      }
    }

    // 如果所有单词都已缓存，直接返回
    if (uncachedWords.length === 0) {
      console.log('所有单词都使用缓存的翻译结果');
      return cachedTranslations;
    }

    // 需要翻译的单词
    console.log(`需要翻译 ${uncachedWords.length} 个单词:`, uncachedWords);

    // 翻译未缓存的单词
    let newTranslations = [];

    try {
      console.log('尝试使用主要翻译服务翻译单词列表...');
      // 尝试使用主要翻译服务
      newTranslations = await primaryTranslator.translateWords(uncachedWords, direction);
    } catch (primaryError) {
      console.warn('主要翻译服务翻译单词列表失败，尝试使用备用翻译服务:', primaryError);

      // 尝试使用备用翻译服务
      newTranslations = await backupTranslator.translateWords(uncachedWords, direction);
    }
    
    // 缓存新翻译的结果
    for (let i = 0; i < uncachedWords.length; i++) {
      const word = uncachedWords[i];
      const translation = newTranslations[i];
      const originalIndex = wordIndexMap.get(word);

      // 更新结果数组
      cachedTranslations[originalIndex] = translation;

      // 缓存结果
      const cacheKey = `${word}_${sourceLanguage}_${targetLanguage}`;
      translationCache.set(cacheKey, {
        src: word,
        dst: translation,
        from: sourceLanguage,
        to: targetLanguage
      });
      console.log("00000000000-",cacheKey)
    }

    // 保存到本地存储
    saveCacheToStorage();
    console.log("-----",cachedTranslations)
    return cachedTranslations;
  } catch (error) {
    console.error('所有翻译服务翻译单词列表都失败:', error);
    // 如果翻译失败，返回原始单词列表
    return words;
  }
};

/**
 * 翻译单个单词
 * @param {string} word - 要翻译的单词
 * @param {string} direction - 翻译方向，'en2zh'（英文到中文）或 'zh2en'（中文到英文）
 * @returns {Promise<string>} - 翻译结果
 */
export const translateWord = async (word, direction = 'en2zh') => {
  try {
    if (!word || typeof word !== 'string') {
      return Promise.reject(new Error('单词不能为空'));
    }

    // 检查是否包含逗号，如果包含，可能是多个单词
    if (word.includes(',') || word.includes('，')) {
      console.log('检测到逗号分隔的多个单词，作为单词列表处理');
      // 将输入按逗号分割，并去除空白
      const wordList = word.split(/[,，]/).map(w => w.trim()).filter(w => w);

      // 如果确实是多个单词，直接调用translateWords
      if (wordList.length > 1) {
        const translations = await translateWords(wordList, direction);
        // 返回与原格式相同的结果（用逗号连接）
        return translations.join(', ');
      }
    }

    // 设置源语言和目标语言
    const sourceLanguage = direction === 'en2zh' ? 'en' : 'zh';
    const targetLanguage = direction === 'en2zh' ? 'zh' : 'en';

    // 检查缓存
    const cacheKey = `${word}_${sourceLanguage}_${targetLanguage}`;
    if (translationCache.has(cacheKey)) {
      console.log('使用缓存的单词翻译结果:', word);
      return translationCache.get(cacheKey).dst;
    }

    // 未缓存，需要翻译
    let translation;

    try {
      console.log('尝试使用主要翻译服务翻译单词...');
      // 尝试使用主要翻译服务
      translation = await primaryTranslator.translateWord(word, direction);
    } catch (primaryError) {
      console.warn('主要翻译服务翻译单词失败，尝试使用备用翻译服务:', primaryError);

      // 尝试使用备用翻译服务
      translation = await backupTranslator.translateWord(word, direction);
    }

    // 缓存结果
    translationCache.set(cacheKey, {
      src: word,
      dst: translation,
      from: sourceLanguage,
      to: targetLanguage
    });

    // 保存到本地存储
    saveCacheToStorage();

    return translation;
  } catch (error) {
    console.error('所有翻译服务翻译单词都失败:', error);
    // 如果翻译失败，返回原单词
    return word;
  }
};

/**
 * 检测语言类型
 * @param {string} text - 要检测的文本
 * @returns {string} - 返回语言类型，'en'（英文）或 'zh'（中文）
 */
export const detectLanguage = (text) => {
  // 两个翻译器的语言检测逻辑相似，使用任一个即可
  return primaryTranslator.detectLanguage(text);
};

/**
 * 智能翻译（自动检测语言并翻译）
 * @param {string} text - 要翻译的文本
 * @returns {Promise<string>} - 翻译结果
 */
export const smartTranslate = async (text) => {
  try {
    if (!text) {
      return Promise.reject(new Error('文本不能为空'));
    }

    // 检查是否是逗号分隔的单词列表
    if (text.includes(',') || text.includes('，')) {
      console.log('智能翻译检测到逗号分隔的文本');
      // 检测第一个单词的语言来确定整个列表的语言
      const firstWord = text.split(/[,，]/)[0].trim();
      if (firstWord) {
        const sourceLanguage = detectLanguage(firstWord);
        const translationDirection = sourceLanguage === 'zh' ? 'zh2en' : 'en2zh';

        // 使用translateWord处理，它已经能处理逗号分隔的列表
        return await translateWord(text, translationDirection);
      }
    }

    // 检测语言
    const sourceLanguage = detectLanguage(text);
    const targetLanguage = sourceLanguage === 'zh' ? 'en' : 'zh';

    // 检查缓存
    const cacheKey = `${text}_${sourceLanguage}_${targetLanguage}`;
    if (translationCache.has(cacheKey)) {
      console.log('使用缓存的智能翻译结果');
      return translationCache.get(cacheKey).dst;
    }

    // 未缓存，需要翻译
    let result;

    try {
      console.log('尝试使用主要翻译服务智能翻译...');
      // 尝试使用主要翻译服务
      result = await primaryTranslator.smartTranslate(text);
    } catch (primaryError) {
      console.warn('主要翻译服务智能翻译失败，尝试使用备用翻译服务:', primaryError);

      // 尝试使用备用翻译服务
      result = await backupTranslator.smartTranslate(text);
    }

    // 缓存结果
    translationCache.set(cacheKey, {
      src: text,
      dst: result,
      from: sourceLanguage,
      to: targetLanguage
    });

    // 保存到本地存储
    saveCacheToStorage();

    return result;
  } catch (error) {
    console.error('所有翻译服务智能翻译都失败:', error);
    // 如果翻译失败，返回原文
    return text;
  }
};

/**
 * 清理翻译缓存（内存+本地存储）
 */
export function clearTranslationCache() {
  translationCache.clear();
  uni.removeStorageSync(STORAGE_KEY);
  uni.removeStorageSync(STORAGE_EXPIRY_KEY);
  console.log('翻译缓存已清理');
}

export default {
  translateText,
  translateWords,
  translateWord,
  detectLanguage,
  smartTranslate,
  clearTranslationCache
};
