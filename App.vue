<script>
	export default {
		onLaunch: function() {
			console.log('App Launch');
			// 确保这里没有阻止导航的代码
			
			// 添加全局导航拦截器
			uni.addInterceptor('navigateTo', {
				success(args) {
					console.log('导航拦截器 - 成功:', args);
					return args;
				},
				fail(err) {
					console.log('导航拦截器 - 失败:', err);
					return false;
				}
			});
		},
		onShow: function() {
			console.log('App Show');
		},
		onHide: function() {
			console.log('App Hide');
		}
	}
</script>

<style lang="scss">
	@import '@/static/styles/common.scss';
</style>
