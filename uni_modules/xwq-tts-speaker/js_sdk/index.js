class TTSSpeaker {
  constructor() {
    this.isAndroid = plus.os.name === 'Android';
    this.isIOS = plus.os.name === 'iOS';
  }

  // iOS 平台
  start(options) {
    const { content, speechRate = 0.5, pitch = 1.0, language = 'zh-CN', finish } = options;
    if (this.isIOS) {
      plus.speech.startSpeaking(content, {
        rate: speechRate,
        pitch: pitch,
        lang: language,
        success: finish,
        fail: (err) => {
          console.error('iOS TTS error:', err);
        }
      });
    }
  }

  // Android 平台
  init(options) {
    const { enginesName, speechRate = 0.5, pitch = 1.0, success, fail, finshed } = options;
    if (this.isAndroid) {
      plus.speech.startSpeaking('', {
        engine: enginesName,
        rate: speechRate,
        pitch: pitch,
        success: success,
        fail: fail,
        onend: finshed
      });
    }
  }

  startSpeak(text) {
    if (this.isAndroid) {
      plus.speech.startSpeaking(text);
    }
  }

  stop() {
    plus.speech.stopSpeaking();
  }

  stopSpeak() {
    this.stop();
  }
}

export function CreateTTSSpeaker() {
  return new TTSSpeaker();
} 