# xwq-tts-speaker
### 利用系统TTS将文本转成语音播报，可以自定义第三方TTS引擎(仅支持androi端)

* Methods 方法（安卓）
   * init  初始化
   * startSpeak  开始播报
   * setSpeechRate  设置速度
   * getEngines  获取引擎列表

* Methods 方法（ios）
   * start 开始
   * pause 暂停
   * contine 继续
   * stop 停止

* ### 代码示例

#### 在UniappX项目中使用(Android示例)
---
```javascript
<template>
	<view>
		<textarea @input="iptHandle" name="test" id="tts" cols="30" rows="10"
			style="width: 100%;border: 1px solid #ccc;;padding: 10;background-color: #f2f2f2;margin-bottom: 10px;"></textarea>
		<button type="default" @click="start">文本转语音播报</button>
		<button type="default" @click="stop">播报暂停</button>
		<button type="default" @click="setSpeechRate">设置播报速度</button>
		<button type="default" @click="getEngines">获取手机播报引擎</button>
	</view>
	<view class="eng-list" style="padding: 15px;">
		<view class="title">
			<text style="font-weight: bold;font-size: 20px;">引擎列表：</text>
		</view>
		<view v-for="(i,k) in engineList" :key='k' class="item">
			{{i}}
		</view>
	</view>
</template>

<script setup>
	import { CreateTTSSpeaker, Option, Info } from "@/uni_modules/xwq-tts-speaker";

	const iptVal = ref("");
	const engineList=ref([] as string[]);
	const TTS = new CreateTTSSpeaker();//创建TTS实例

	const iptHandle = (e : UniInputEvent) => {
		iptVal.value = e.detail.value;
	};

	//开始播报，在开始之前要先初始化TTS，成功后再开始
	const start = () => {
		TTS.init({
			enginesName: "com.google.android.tts", //自定义第三方TTS引擎，前提是手机上已安装
			speechRate: 0.1,//播报速度，有些系统TTS不支持设置
			pitch:0.4,//语调设置，默认1，越小越像男生
			success: (val : Info) => {
				console.log(val);
				TTS.startSpeak(iptVal.value);//开始播报文本
			},
			fail: (val : Info) => {
				console.log(val);
			},
			finshed: () => {
				console.log('播报结束')
			}
		} as Option);
	};
	//停止播报
	const stop = () => {
		TTS.stopSpeak();
	};
	//设置播报速度，有些系统TTS不支持
	const setSpeechRate = () => {
		TTS.setSpeechRate(0.5);
	};
	//获取引擎列表，没有默认系统TTS
	const getEngines = () => {
		let result = TTS.getEngines();
		engineList.value = result;
	}
</script>
```

#### 在Uniapp项目中使用
-----
```javascript
<template>
	<view>
		<view class="area">
			<textarea @input="iptHandle"
				style="width: 100%;border: 1px solid #ccc;padding: 15px;background-color: #f2f2f2;margin-bottom: 15px;" />
			<button type="default" @click="startHandle">开始播报</button>
			<button type="default" @click="stop">播报暂停</button>
			<button type="default" @click="setSpeechRate">设置播报速度</button>
			<button type="default" @click="getEngines">获取手机播报引擎</button>
		</view>
		<view class="eng-list" style="padding: 15px;">
			<view class="title">
				<text style="font-weight: bold;font-size: 20px;">引擎列表：</text>
			</view>
			<view v-for="(i,k) in engineList" :key='k' class="item">
				{{i}}
			</view>
		</view>
	</view>
</template>

<script>
	import CreateTTSSpeaker from "@/uni_modules/xwq-tts-speaker";

	let TTS = new CreateTTSSpeaker();//创建TTS实例

	export default {
		data() {
			return {
				iptVal: "",
				engineList: []
			}
		},
		methods: {
			iptHandle(e) {
				this.iptVal = e.detail.value;
			},
			startHandle() {
				TTS.init({
					enginesName: "com.google.android.tts", //自定义第三方TTS引擎，前提是手机上已安装
					speechRate: 0.1,//播报速度，有些系统TTS不支持设置
					pitch:0.4,//语调设置，默认1，越小越像男生
					success: (val) => {
						console.log(val);
						TTS.startSpeak(this.iptVal);//开始播报文本
					},
					fail: (val) => {
						console.log('TTS初始化失败');
					},
					finshed: () => {
						console.log('播报完成');
					}
				})
			},
			//停止播报
			stop() {
				TTS.stopSpeak();
			},
			//设置播报速度，有些系统TTS不支持
			setSpeechRate() {
				TTS.setSpeechRate(0.5);
			},
			//获取引擎列表，没有默认系统TTS
			getEngines() {
				let result = TTS.getEngines();
				this.engineList = result;
			}
		}
	}
</script>

```


#### 在UniappX项目中使用(IOS示例)
```javascript
<template>
	<view>
		<button type="default" @click="start">开始播报</button>
		<button type="default" @click="pause">暂停播报</button>
		<button type="default" @click="contine">继续播报</button>
		<button type="default" @click="stop">停止播报</button>
	</view>
</template>

<script setup>
	import { startSpeech,pauseSpeech,continueSpeech,stopSpeech, Option } from '@/uni_modules/xwq-tts-speaker';
	const start=()=>{
		startSpeech({
			content:"我家住在一个遥远的地方，美丽又安静！",//内容
			speechRate:0.5,//设置语速，范围 0.0~1.0
			pitch:0.9,// 音高，范围 0.5~2.0
			language:'zh-CN',//语言
			finish:()=>{
				console.log('播报结束')
			}
		}as Option)
	};
	
	const pause=()=>{
		pauseSpeech()
	};
	const contine=()=>{
		continueSpeech()
	};
	const stop=()=>{
		stopSpeech()
	};
</script>

```