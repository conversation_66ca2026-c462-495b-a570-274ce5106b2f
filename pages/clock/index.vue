<template>
  <view class="page-container">
    <!-- 小时图标区域 -->
    

    <!-- 主计时器区域 -->
    <view class="part1">
      <view class="clock-container">
      
      <!-- 动态数量的圆环段 -->
      <view class="progress-ring">
        <view
          v-for="segment in dynamicSegments"
          :key="'segment-' + segment"
          class="ring-segment"
          :class="getSegmentClass(segment - 1)"
          :style="getSegmentStyle(segment - 1)"
        ></view>
      </view>
      <view class="hour-icons">
        <view
          v-for="(hour, index) in hourIcons"
          :key="index"
          class="hour-icon"
          :class="{ 'filled': hour.filled, 'partial': hour.partial }"
          :style="{ '--progress': hour.progress + '%' }"
        ></view>
      </view>

      <!-- 打点标记 
      <view v-if="showDotMarks" class="dot-marks">
        <view
          v-for="(dot, index) in dotMarks"
          :key="'dot-mark-' + index"
          class="dot-mark"
          :style="getDotMarkStyle(dot.seconds)"
        ></view>
      </view>-->

      <view class="clock-dial">

        <!-- 当前时间显示 -->
        <view class="current-time">{{ currentTimeDisplay }}</view>

        <!-- 计时器时间显示 -->
        <view class="clock-time">{{ formatTime(displaySeconds) }}</view>

        <!-- 刻度
        <view
          v-for="i in 12"
          :key="'tick'+i"
          class="clock-tick"
          :style="getTickStyle(i-1)"
        ></view> -->

        <!-- 数字 -->
        <view
          v-for="i in 12"
          :key="'num'+i"
          class="clock-num"
          :style="getNumStyle(i-1)"
        >
        <view class="nn" :style="getNumNNStyle(i-1)">{{ (i-1)*5 }}</view>
        </view>



        <!-- 打点按钮 -->
        <view
          v-if="isRunning && !isPaused"
          class="dot-btn"
          @click="addDotMark"
        >
          打点
        </view>
        <button v-if="!isRunning && !isPaused && (currentSeconds > 0 || sliderProgress > 0 || isEnded)" class="clock-btn reset-btn" @click="resetTimer">重置计时器</button>
      </view>
      
    </view>
      <!-- 垂直拖动条 -->
      <view v-if="!isRunning && !isEnded" class="drag-slider-container">
        <view class="drag-slider-track">
          <!-- 拖动条背景 -->
          <view class="drag-slider-bg"></view>
          <!-- 拖动条进度 -->
          <view
            class="drag-slider-progress"
            :style="{ height: sliderProgress + '%' }"
          ></view>
          <!-- 拖动手柄 -->
          <view
            class="drag-slider-handle"
            :style="{ top: sliderProgress + '%' }"
            @touchstart="onSliderTouchStart"
            @touchmove="onSliderTouchMove"
            @touchend="onSliderTouchEnd"
          ></view>
        </view>
        <!-- 时间显示 -->
        
        </view>
        <view v-if="!isRunning && !isEnded" class="touch-tip">
        倒计时<br>拖动↓
        </view>
    </view>
    <view class="part2">
      <!-- 操作按钮区 -->
      <view class="clock-actions">
        <button v-if="!isRunning && !isPaused && !isEnded" class="clock-btn start-btn" @click="startTimer">开始计时</button>
        <button v-if="isRunning && !isPaused" class="clock-btn pause-btn" @click="pauseTimer">暂停</button>
        <button v-if="isPaused" class="clock-btn resume-btn" @click="resumeTimer">继续</button>
        <button v-if="isRunning || isPaused" class="clock-btn end-btn" @click="stopTimer">结束计时</button>
        
      </view>

      <!-- 动态圆环控制 暂时隐藏 -->
      <view v-if="!isRunning && false" class="ring-control">
        <button class="control-btn" :class="{ 'active': enableDynamicRing }" @click="toggleDynamicRing">
          {{ enableDynamicRing ? '动态圆环：开' : '动态圆环：关' }}
        </button>

      </view>

      <!-- 音频测试按钮 - 开发时使用 -->
      <view v-if="!isRunning && false" class="audio-test">
        <button class="test-btn" @click="testAudioPlay">测试音频</button>
        <button class="test-btn stop-btn" @click="testAudioStop">停止音频</button>
      </view>

      <!-- 打点列表 -->
      <view v-if="showDotList" class="dot-list">
        <view class="dot-list-title">
          打点记录
          <!-- <button
            v-if="isCountdown && dotMarks.length > 0"
            class="toggle-segments-btn"
            @click="toggleShowAllSegments"
          >
            {{ showAllSegments ? '隐藏已过去时间' : '显示所有时间段' }}
          </button>-->
        </view>
        <view class="dot-items">
          <view
            v-for="(dot, index) in dotMarks"
            :key="'list-dot-' + index"
            class="dot-item"
            :class="{ 'selected': selectedDotIndex === index }"
            @click="selectDot(index)"
          >
            <text class="dot-label">{{ index + 1 }}</text>
            <text class="dot-time">{{ formatTime(dot.seconds) }}</text>
          </view>
        </view>
      </view>
      </view>
    
    

    

  </view>
</template>

<script>
import keepAlive from '@/utils/keepAlive.js';

export default {
  data() {
    return {
      clockSize: 640, // 时钟尺寸 (rpx)
      center: { x: 320, y: 320 }, // 中心点 (rpx)

      // 计时相关
      totalSeconds: 0,
      currentSeconds: 0,
      startTime: 0,
      isRunning: false,
      isPaused: false,
      isEnded: false, // 是否已结束计时（区别于初始状态）
      timer: null,
      isCountdown: false, // 是否为倒计时模式

      // 保活相关
      lastUpdateTime: 0, // 上次更新时间戳
      backgroundStartTime: 0, // 进入后台时间
      keepAliveTimer: null, // 保活定时器
      heartbeatTimer: null, // 心跳检测定时器

      // 打点相关
      dotMarks: [],
      showDotList: false,
      selectedDotIndex: -1,
      showDotMarks: false,

      // 小时图标
      hourIcons: [],

      // 圆环相关
      ringRadius: 280, // 圆环半径 (rpx)

      // 拖动条相关
      sliderProgress: 0, // 拖动条进度 (0-100)
      sliderDragging: false,
      maxCountdownSeconds: 7200, // 最大倒计时2小时

      // 圆环动态元素配置
      enableDynamicRing: true, // 是否启用动态圆环效果
      baseSegments: 180, // 基础段数（1小时）

      // 打点显示控制
      showAllSegments: false, // 是否显示所有时间段（包括倒计时已过去的）

      // 音频相关
      audioContext: null, // 音频上下文

      // 当前时间显示
      currentTimeDisplay: '', // 当前时间显示（HH:MM格式）
      timeUpdateInterval: null, // 时间更新定时器
    }
  },
  computed: {
    // 当前进度的分钟数（0-59）
    currentMinute() {
      if (this.isCountdown) {
        // 倒计时：显示剩余时间的分钟
        return Math.floor((this.currentSeconds % 3600) / 60);
      } else {
        // 正计时：显示已用时间的分钟
        return Math.floor((this.currentSeconds % 3600) / 60);
      }
    },
    displaySeconds() {
      return this.currentSeconds;
    },

    // 动态计算圆环段数
    dynamicSegments() {
      if (!this.enableDynamicRing) {
        return this.baseSegments; // 固定180段
      }

      // 根据当前时间或设置时间计算需要的段数
      const targetSeconds = this.isRunning ? this.currentSeconds : this.totalSeconds;

      if (targetSeconds <= 3600) {
        // 1小时内：固定180段
        return this.baseSegments;
      } else {
        // 超过1小时：每20秒增加1段，最多增加180段
        const extraSeconds = targetSeconds - 3600;
        const extraSegments = Math.min(
          Math.floor(extraSeconds / 20), // 每20秒增加1段
          this.baseSegments // 最多再增加180段（总共360段）
        );
        return this.baseSegments + extraSegments;
      }
    },

    // 每段代表的秒数
    secondsPerSegment() {
      return 20; // 固定每段20秒
    },

    // 圆环配置信息
    ringInfo() {
      const targetSeconds = this.isRunning ? this.currentSeconds : this.totalSeconds;

      let info = `${this.dynamicSegments}段`;

      if (this.enableDynamicRing && targetSeconds > 3600) {
        const baseSegments = this.baseSegments;
        const extraSegments = this.dynamicSegments - baseSegments;
        const extraSeconds = targetSeconds - 3600;
        info += ` (第1圈${baseSegments} + 第2圈${extraSegments})`;
        info += ` | 超时${Math.floor(extraSeconds/60)}分${extraSeconds%60}秒`;
      }

      return info;
    }
  },
  mounted() {
    // 初始化小时图标
    this.updateHourIcons();

    // 初始化当前时间显示
    this.updateCurrentTimeDisplay();

    // 每秒更新一次当前时间显示
    this.timeUpdateInterval = setInterval(() => {
      this.updateCurrentTimeDisplay();
    }, 1000); // 1秒更新一次，实时显示

    // 启动心跳检测
    this.startHeartbeat();

    // 监听页面生命周期
    this.setupPageLifecycle();

    // 初始化保活机制
    this.initKeepAlive();

    this.$nextTick(() => {
      uni.createSelectorQuery().in(this).select('.clock-dial').boundingClientRect(rect => {
        if (rect) {
          this.center = {
            x: rect.left + rect.width / 2,
            y: rect.top + rect.height / 2
          }
        }
      }).exec();
    });
  },

  // 监听数据变化，确保小时图标实时更新
  watch: {
    totalSeconds() {
      // 当设置的总时间改变时，更新小时图标
      if (!this.isRunning) {
        this.updateHourIcons();
      }
    },
    currentSeconds() {
      // 当前时间改变时，如果在运行中则更新小时图标
      if (this.isRunning) {
        this.updateHourIcons();
      }
    }
  },

  // 页面销毁时清理资源
  beforeDestroy() {
    this.clearAllTimers();
    if (this.audioContext) {
      this.audioContext.destroy();
      this.audioContext = null;
    }
    // 停止保活机制
    keepAlive.stop();
  },

  // 页面显示时的处理
  onShow() {
    console.log('页面显示，检查计时器状态');
    if (this.isRunning && !this.isPaused) {
      this.handlePageShow();
    }
  },

  // 页面隐藏时的处理
  onHide() {
    console.log('页面隐藏，记录时间');
    if (this.isRunning && !this.isPaused) {
      this.handlePageHide();
    }
  },

  methods: {
    // 拖动条触摸开始
    onSliderTouchStart(e) {
      if (this.isRunning) return;
      this.sliderDragging = true;
      e.preventDefault();
    },

    // 拖动条触摸移动
    onSliderTouchMove(e) {
      if (!this.sliderDragging || this.isRunning) return;
      e.preventDefault();

      const touch = e.touches[0];
      this.updateSliderProgress(touch.clientY);
    },

    // 拖动条触摸结束
    onSliderTouchEnd() {
      if (!this.sliderDragging || this.isRunning) return;
      this.sliderDragging = false;
    },
    // 更新拖动条进度
    updateSliderProgress(clientY) {
      uni.createSelectorQuery().in(this).select('.drag-slider-track').boundingClientRect(rect => {
        if (rect) {
          const relativeY = clientY - rect.top;
          const progress = Math.max(0, Math.min(100, (relativeY / rect.height) * 100));

          this.sliderProgress = progress;
          this.updateTimeBySlider();
        }
      }).exec();
    },

    // 根据拖动条进度更新时间
    updateTimeBySlider() {
      // 将进度转换为分钟数（0-120分钟，即0-2小时），然后转为秒
      const minutes = Math.round((this.sliderProgress / 100) * 120);
      const seconds = minutes * 60;

      this.totalSeconds = seconds;
      this.currentSeconds = seconds;

      if (seconds > 0) {
        this.isCountdown = true;
      } else {
        this.isCountdown = false;
      }

      // 实时更新左上角的小时图标
      this.updateHourIcons();
    },

    // 统一的开始计时方法
    startTimer() {
      if (this.totalSeconds > 0) {
        // 有设置时间，开始倒计时
        this.isCountdown = true;
        this.startTime = Date.now();
      } else {
        // 没有设置时间，开始正计时
        this.isCountdown = false;
        this.currentSeconds = 0;
        this.totalSeconds = 0;
        this.startTime = Date.now();
      }

      this.dotMarks = [];
      this.showDotList = false;
      this.showDotMarks = false;
      this.showAllSegments = false; // 开始计时时隐藏所有时间段
      this.isEnded = false; // 清除结束状态
      this.updateHourIcons();
      this.startTimerLoop();
    },

    // 重置计时器
    resetTimer() {
      this.isRunning = false;
      this.isPaused = false;
      this.isEnded = false; // 清除结束状态，回到初始状态
      this.currentSeconds = 0;
      this.totalSeconds = 0;
      this.sliderProgress = 0;
      this.isCountdown = false;
      this.dotMarks = [];
      this.showDotList = false;
      this.showDotMarks = false;
      this.showAllSegments = false; // 重置时隐藏所有时间段
      this.updateHourIcons();

      // 清除计时器
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }

      // 停止闹钟铃声
      this.stopCountdownSound();
    },

    // 启动计时器循环（改进版）
    startTimerLoop() {
      if (this.isRunning) return;

      this.isRunning = true;
      this.isPaused = false;
      this.lastUpdateTime = Date.now();

      // 使用更可靠的计时方式
      this.timer = setInterval(() => {
        if (this.isPaused) return;

        const now = Date.now();
        const elapsed = Math.floor((now - this.lastUpdateTime) / 1000);

        // 检测时间跳跃（可能是从后台恢复）
        if (elapsed > 2) {
          console.log(`检测到时间跳跃: ${elapsed}秒，可能从后台恢复`);
          this.handleTimeJump(elapsed);
        } else {
          this.updateTimer();
        }

        this.lastUpdateTime = now;
      }, 1000);

      // 启动保活机制
      this.startKeepAlive();

      // 启动应用保活
      this.startAppKeepAlive();
    },

    // 更新计时器
    updateTimer() {
      if (this.isCountdown) {
        // 倒计时
        if (this.currentSeconds > 0) {
          this.currentSeconds--;
        } else {
          this.stopTimer();
          this.playCountdownEndSound();
          uni.showToast({
            title: '倒计时结束！',
            icon: 'success'
          });
        }
      } else {
        // 正计时
        this.currentSeconds++;
        // 正计时时也更新totalSeconds，以便动态圆环能响应
        if (this.currentSeconds > this.totalSeconds) {
          this.totalSeconds = this.currentSeconds;
        }
      }

      this.updateHourIcons();
    },

    // 暂停计时
    pauseTimer() {
      this.isPaused = true;
      // 暂停时不改变 isEnded 状态
    },

    // 继续计时
    resumeTimer() {
      this.isPaused = false;
      // 继续时不改变 isEnded 状态
    },

    // 停止计时
    stopTimer() {
      this.isRunning = false;
      this.isPaused = false;
      this.isEnded = true; // 设置为结束状态

      // 清理所有定时器
      this.clearAllTimers();
      this.sliderProgress = 0;

      // 停止保活机制
      this.stopAppKeepAlive();

      // 停止可能正在播放的闹钟铃声
      this.stopCountdownSound();

      // 如果有打点记录，显示打点列表和所有时间段
      if (this.dotMarks.length > 0) {
        this.showDotList = true;
        this.showDotMarks = true;
        this.showAllSegments = true; // 显示所有时间段，包括倒计时已过去的
      }
    },

    // 添加打点
    addDotMark() {
      if (!this.isRunning || this.isPaused) return;

      // 计算当前时间所在的20秒节点
      const currentInterval = Math.floor(this.currentSeconds / 20);

      // 检查当前20秒节点是否已经打过点
      const existingDot = this.dotMarks.find(dot => {
        const dotInterval = Math.floor(dot.seconds / 20);
        return dotInterval === currentInterval;
      });

      if (existingDot) {
        // uni.showToast({
        //   title: '当前时间段已打点',
        //   icon: 'none',
        //   duration: 1500
        // });
        return;
      }

      const currentTime = this.currentSeconds;

      this.dotMarks.push({
        seconds: currentTime,
        timestamp: Date.now(),
        interval: currentInterval // 记录所在的20秒节点
      });

      // uni.showToast({
      //   title: `打点 ${this.dotMarks.length}`,
      //   icon: 'success',
      //   duration: 1000
      // });
    },

    // 选择打点
    selectDot(index) {
      this.selectedDotIndex = index;
      // 这里可以添加高亮显示逻辑
    },

    // 更新小时图标
    updateHourIcons() {
      // 根据不同状态选择显示的时间
      let displaySeconds;

      if (!this.isRunning && this.isCountdown) {
        // 设置倒计时状态：显示设置的总时间
        displaySeconds = this.totalSeconds;
      } else {
        // 运行中或正计时：显示当前时间
        displaySeconds = this.currentSeconds;
      }

      const hours = Math.floor(displaySeconds / 3600);
      const currentHourSeconds = displaySeconds % 3600;

      this.hourIcons = [];

      // 添加完整的小时图标
      for (let i = 0; i < hours; i++) {
        this.hourIcons.push({
          filled: true,
          partial: false,
          progress: 100
        });
      }

      // 添加当前小时的图标（如果有进度）
      if (currentHourSeconds > 0 || hours === 0) {
        const progress = currentHourSeconds / 3600 * 100;

        this.hourIcons.push({
          filled: false,
          partial: true,
          progress: progress
        });
      }
    },
    getNumStyle(idx) {
      const angle = idx * 30;
      return {
        transform: `rotate(${angle}deg)`,
        transformOrigin: `center 258rpx` // 320rpx * 0.32 + 60rpx = 258rpx
      }
    },
    getNumNNStyle(idx) {
      const angle = idx * 30;
      return {
        transform: `rotate(-${angle}deg)`
      }
    },

    // 获取圆环段的样式
    getSegmentStyle(segmentIndex) {
      let angle;

      if (segmentIndex < this.baseSegments) {
        // 第一圈：0-179段，每段2度（180段 = 360度）
        angle = segmentIndex * 2;
      } else {
        // 第二圈：从0度重新开始覆盖
        const secondCircleIndex = segmentIndex - this.baseSegments;
        // 第二圈按照实际增加的段数分布，但最多覆盖360度
        angle = secondCircleIndex * 2; // 保持每段2度的间距
      }

      const ringRadius = 280; // 640rpx / 2 - 40rpx = 280rpx
      const x = 320 + ringRadius * Math.sin(angle * Math.PI / 180); // 320rpx 是中心点
      const y = 320 - ringRadius * Math.cos(angle * Math.PI / 180);

      return {
        position: 'absolute',
        left: x - 3 + 'rpx', // 6rpx / 2 = 3rpx
        top: y - 10 + 'rpx', // 20rpx / 2 = 10rpx
        transform: `rotate(${angle}deg)`,
        transformOrigin: 'center'
      };
    },

    // 获取圆环段的类名
    getSegmentClass(segmentIndex) {
      const classes = [];

      // 动态计算每段代表的秒数
      let segmentSeconds;

      if (segmentIndex < this.baseSegments) {
        // 第一圈：前180段，每段代表20秒（0-3600秒）
        segmentSeconds = (segmentIndex + 1) * 20;
      } else {
        // 第二圈：重新从3600秒开始，覆盖在第一圈上
        const extraIndex = segmentIndex - this.baseSegments;
        const extraSegments = this.dynamicSegments - this.baseSegments;
        if (extraSegments > 0) {
          // 第二圈每段代表的秒数（基于实际的额外时间）
          const secondCircleSecondsPerSegment = 20; // 保持每段20秒
          segmentSeconds = 3600 + (extraIndex + 1) * secondCircleSecondsPerSegment;
        } else {
          segmentSeconds = (segmentIndex + 1) * 20;
        }
      }

      if (this.isCountdown) {
        // 倒计时模式：当整个20秒区间用完时才隐藏格子
        // 计算当前剩余时间对应的20秒区间数
        const remainingIntervals = Math.ceil(this.currentSeconds / 20);
        const segmentInterval = Math.ceil(segmentSeconds / 20);

        if (segmentInterval <= remainingIntervals) {
          classes.push('active');
        }

        // 倒计时已过去的时间段（隐藏但不删除）
        if (segmentInterval > remainingIntervals && segmentSeconds <= this.totalSeconds) {
          if (this.showAllSegments) {
            // 显示所有时间段时，已过去的时间段也显示但样式不同
            classes.push('passed-visible');
          } else {
            // 正常情况下隐藏已过去的时间段
            classes.push('passed');
          }
        }
      } else {
        // 正计时模式：开始计时1秒时，第一个20秒格子就高亮
        // 计算当前处于第几个20秒区间
        const currentInterval = Math.floor(this.currentSeconds / 20) + 1;
        const segmentInterval = Math.ceil(segmentSeconds / 20);

        if (segmentInterval <= currentInterval) {
          classes.push('active');
        }
      }

      // 检查是否在打点区间内
      if (this.selectedDotIndex >= 0 && this.dotMarks.length > 0) {
        const selectedDot = this.dotMarks[this.selectedDotIndex];
        let prevDot;

        if (this.isCountdown) {
          // 倒计时：打点区间逻辑相反
          prevDot = this.selectedDotIndex > 0 ? this.dotMarks[this.selectedDotIndex - 1] : { seconds: this.totalSeconds };

          if (segmentSeconds <= prevDot.seconds && segmentSeconds >= selectedDot.seconds) {
            classes.push('highlighted');
          }
        } else {
          // 正计时：正常打点区间
          prevDot = this.selectedDotIndex > 0 ? this.dotMarks[this.selectedDotIndex - 1] : { seconds: 0 };

          if (segmentSeconds >= prevDot.seconds && segmentSeconds <= selectedDot.seconds) {
            classes.push('highlighted');
          }
        }
      }

      // 检查是否是打点位置（计时过程中也显示）
      if (this.isTimestampPosition(segmentSeconds)) {
        classes.push('timestamp');
        // 打点位置始终显示，即使在倒计时的已过去时间段
        if (this.isCountdown && segmentSeconds > this.currentSeconds) {
          classes.push('timestamp-visible');
        }
      }

      // 添加圈数标识
      if (segmentIndex >= this.baseSegments) {
        classes.push('second-circle');
      } else {
        classes.push('first-circle');
      }

      return classes.join(' ');
    },

    // 检查是否是打点位置
    isTimestampPosition(segmentSeconds) {
      return this.dotMarks.some(dot => {
        const dotTime = dot.seconds;
        // 动态计算误差范围
        const tolerance = (3600 / this.dynamicSegments) / 2; // 半段的时间作为误差范围
        return Math.abs(segmentSeconds - dotTime) <= tolerance;
      });
    },

    // 获取打点标记的样式
    getDotMarkStyle(dotSeconds) {
      // 将秒数转换为角度
      const angle = (dotSeconds % 3600) / 3600 * 360; // 每小时一圈
      const markRadius = 310; // 280rpx + 30rpx = 310rpx
      const x = 320 + markRadius * Math.sin(angle * Math.PI / 180);
      const y = 320 - markRadius * Math.cos(angle * Math.PI / 180);

      return {
        position: 'absolute',
        left: x - 12 + 'rpx', // 24rpx / 2 = 12rpx
        top: y - 12 + 'rpx'
      };
    },

    // 格式化时间显示
    formatTime(sec) {
      if (!sec || sec <= 0) return '00:00:00';
      const h = Math.floor(sec / 3600);
      const m = Math.floor((sec % 3600) / 60);
      const s = sec % 60;
      return `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}`;
    },

    // 更新当前时间显示（HH:MM格式，实时更新）
    updateCurrentTimeDisplay() {
      const now = new Date();
      const hours = now.getHours().toString().padStart(2, '0');
      const minutes = now.getMinutes().toString().padStart(2, '0');
      const newTimeDisplay = `${hours}:${minutes}`;

      // 只有当时间发生变化时才更新，避免不必要的重渲染
      if (this.currentTimeDisplay !== newTimeDisplay) {
        this.currentTimeDisplay = newTimeDisplay;
      }
    },

    // 切换动态圆环效果
    toggleDynamicRing() {
      this.enableDynamicRing = !this.enableDynamicRing;
      uni.showToast({
        title: this.enableDynamicRing ? '已开启动态圆环' : '已关闭动态圆环',
        icon: 'success',
        duration: 1500
      });
    },

    // 切换显示所有时间段
    toggleShowAllSegments() {
      this.showAllSegments = !this.showAllSegments;
      uni.showToast({
        title: this.showAllSegments ? '显示所有时间段' : '隐藏已过去时间段',
        icon: 'success',
        duration: 1500
      });
    },

    // 停止闹钟铃声
    stopCountdownSound() {
      if (this.audioContext) {
        try {
          this.audioContext.stop();
          this.audioContext.destroy();
          this.audioContext = null;
          console.log('闹钟铃声已停止');
        } catch (error) {
          console.error('停止音频时出错:', error);
          this.audioContext = null;
        }
      }
    },

    // 播放倒计时结束声音
    playCountdownEndSound() {
      try {
        // 创建音频上下文
        this.audioContext = uni.createInnerAudioContext();

        // 设置音频文件路径
        this.audioContext.src = '/static/looperman.mp3';

        // 设置音频属性
        this.audioContext.autoplay = true;
        this.audioContext.loop = false;
        this.audioContext.volume = 0.8;

        // 播放成功回调
        this.audioContext.onPlay(() => {
          console.log('倒计时结束音频开始播放');
        });

        // 播放结束回调
        this.audioContext.onEnded(() => {
          console.log('倒计时结束音频播放完成');
          this.audioContext.destroy();
          this.audioContext = null;
        });

        // 播放错误回调
        this.audioContext.onError((error) => {
          console.error('音频播放失败:', error);
          uni.showToast({
            title: '音频播放失败',
            icon: 'none',
            duration: 2000
          });
          this.audioContext.destroy();
          this.audioContext = null;
        });

        // 开始播放
        this.audioContext.play();

      } catch (error) {
        console.error('创建音频上下文失败:', error);
        uni.showToast({
          title: '音频功能不可用',
          icon: 'none',
          duration: 2000
        });
      }
    },

    // 调试方法：检查圆环段的角度分布
    debugSegmentAngles() {
      console.log('=== 圆环段角度分布 ===');
      console.log(`总段数: ${this.dynamicSegments}`);
      console.log(`基础段数: ${this.baseSegments}`);

      // 检查前几个和后几个段的角度
      for (let i = 0; i < Math.min(5, this.dynamicSegments); i++) {
        const style = this.getSegmentStyle(i);
        console.log(`段${i}: ${style.transform}`);
      }

      if (this.dynamicSegments > this.baseSegments) {
        console.log('--- 第二圈开始 ---');
        for (let i = this.baseSegments; i < Math.min(this.baseSegments + 5, this.dynamicSegments); i++) {
          const style = this.getSegmentStyle(i);
          console.log(`段${i}: ${style.transform}`);
        }
      }
    },

    // 调试方法：检查小时图标状态
    debugHourIcons() {
      console.log('=== 小时图标状态 ===');
      console.log(`当前秒数: ${this.currentSeconds}`);
      console.log(`总秒数: ${this.totalSeconds}`);
      console.log(`是否运行中: ${this.isRunning}`);
      console.log(`是否倒计时: ${this.isCountdown}`);
      console.log(`小时图标数量: ${this.hourIcons.length}`);
      this.hourIcons.forEach((icon, index) => {
        console.log(`图标${index}: filled=${icon.filled}, partial=${icon.partial}, progress=${icon.progress}%`);
      });
    },

    // 调试方法：检查打点标记状态
    debugDotMarks() {
      console.log('=== 打点标记状态 ===');
      console.log(`打点数量: ${this.dotMarks.length}`);
      console.log(`显示所有时间段: ${this.showAllSegments}`);
      console.log(`当前时间: ${this.currentSeconds}秒`);
      console.log(`总时间: ${this.totalSeconds}秒`);
      console.log(`当前20秒节点: ${Math.floor(this.currentSeconds / 20)}`);

      this.dotMarks.forEach((dot, index) => {
        const isPassed = this.isCountdown && dot.seconds > this.currentSeconds;
        const dotInterval = Math.floor(dot.seconds / 20);
        console.log(`打点${index + 1}: ${dot.seconds}秒, 节点${dotInterval}, 已过去: ${isPassed}`);
      });

      // 检查几个关键时间段的样式
      for (let i = 0; i < Math.min(10, this.dynamicSegments); i++) {
        const classes = this.getSegmentClass(i);
        if (classes.includes('timestamp') || classes.includes('passed')) {
          console.log(`段${i}: ${classes}`);
        }
      }
    },

    // 测试音频播放
    testAudioPlay() {
      console.log('测试播放倒计时结束音频');
      this.playCountdownEndSound();
    },

    // 测试停止音频
    testAudioStop() {
      console.log('测试停止闹钟铃声');
      this.stopCountdownSound();
    },

    // 调试方法：检查时间更新
    debugTimeUpdate() {
      console.log('=== 时间更新状态 ===');
      console.log(`当前显示时间: ${this.currentTimeDisplay}`);
      console.log(`系统当前时间: ${new Date().toLocaleTimeString()}`);
      console.log(`时间更新定时器状态: ${this.timeUpdateInterval ? '运行中' : '已停止'}`);
    },

    // 调试方法：检查状态变化
    debugTimerState() {
      console.log('=== 计时器状态 ===');
      console.log(`是否运行中: ${this.isRunning}`);
      console.log(`是否暂停: ${this.isPaused}`);
      console.log(`是否已结束: ${this.isEnded}`);
      console.log(`是否倒计时: ${this.isCountdown}`);
      console.log(`当前时间: ${this.currentSeconds}秒`);
      console.log(`总时间: ${this.totalSeconds}秒`);
      console.log(`拖动条是否显示: ${!this.isRunning && !this.isEnded}`);
      console.log(`开始按钮是否显示: ${!this.isRunning && !this.isPaused && !this.isEnded}`);
      console.log(`重置按钮是否显示: ${!this.isRunning && !this.isPaused && (this.currentSeconds > 0 || this.sliderProgress > 0 || this.isEnded)}`);

      if (!this.isCountdown && this.currentSeconds > 0) {
        const currentInterval = Math.floor(this.currentSeconds / 20) + 1;
        console.log(`正计时当前区间: 第${currentInterval}个20秒区间`);
      }

      if (this.isCountdown && this.currentSeconds >= 0) {
        const remainingIntervals = Math.ceil(this.currentSeconds / 20);
        const totalIntervals = Math.ceil(this.totalSeconds / 20);
        console.log(`倒计时剩余区间: ${remainingIntervals}个，总区间: ${totalIntervals}个`);
        console.log(`剩余时间: ${this.currentSeconds}秒，总时间: ${this.totalSeconds}秒`);
      }
    },

    // ========== 新增保活和生命周期处理方法 ==========

    // 清理所有定时器
    clearAllTimers() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
      if (this.timeUpdateInterval) {
        clearInterval(this.timeUpdateInterval);
        this.timeUpdateInterval = null;
      }
      if (this.keepAliveTimer) {
        clearInterval(this.keepAliveTimer);
        this.keepAliveTimer = null;
      }
      if (this.heartbeatTimer) {
        clearInterval(this.heartbeatTimer);
        this.heartbeatTimer = null;
      }
    },

    // 启动保活机制
    startKeepAlive() {
      // 每30秒执行一次保活操作
      this.keepAliveTimer = setInterval(() => {
        if (this.isRunning && !this.isPaused) {
          console.log('保活检查 - 计时器正常运行');
          // 可以在这里添加一些轻量级操作来保持应用活跃
          this.checkTimerHealth();
        }
      }, 30000);
    },

    // 启动心跳检测
    startHeartbeat() {
      // 每5秒检查一次计时器健康状态
      this.heartbeatTimer = setInterval(() => {
        if (this.isRunning && !this.isPaused) {
          this.checkTimerHealth();
        }
      }, 5000);
    },

    // 检查计时器健康状态
    checkTimerHealth() {
      const now = Date.now();
      const timeSinceLastUpdate = now - this.lastUpdateTime;

      // 如果超过3秒没有更新，可能计时器出现问题
      if (timeSinceLastUpdate > 3000) {
        console.warn(`计时器可能停止，上次更新距今: ${timeSinceLastUpdate}ms`);
        this.recoverTimer();
      }
    },

    // 恢复计时器
    recoverTimer() {
      console.log('尝试恢复计时器...');
      if (this.isRunning && !this.isPaused) {
        // 重新启动计时器
        if (this.timer) {
          clearInterval(this.timer);
        }
        this.startTimerLoop();

        uni.showToast({
          title: '计时器已自动恢复',
          icon: 'success',
          duration: 2000
        });
      }
    },

    // 处理时间跳跃（从后台恢复）
    handleTimeJump(jumpSeconds) {
      console.log(`处理时间跳跃: ${jumpSeconds}秒`);

      // 根据跳跃的时间更新计时器
      if (this.isCountdown) {
        this.currentSeconds = Math.max(0, this.currentSeconds - jumpSeconds + 1);
        if (this.currentSeconds <= 0) {
          this.stopTimer();
          this.playCountdownEndSound();
          uni.showToast({
            title: '倒计时结束！',
            icon: 'success'
          });
          return;
        }
      } else {
        this.currentSeconds += jumpSeconds - 1;
        if (this.currentSeconds > this.totalSeconds) {
          this.totalSeconds = this.currentSeconds;
        }
      }

      this.updateHourIcons();

      uni.showToast({
        title: `已同步${jumpSeconds}秒`,
        icon: 'none',
        duration: 1500
      });
    },

    // 设置页面生命周期监听
    setupPageLifecycle() {
      // 监听应用进入后台
      uni.onAppHide(() => {
        console.log('应用进入后台');
        if (this.isRunning && !this.isPaused) {
          this.backgroundStartTime = Date.now();
        }
      });

      // 监听应用回到前台
      uni.onAppShow(() => {
        console.log('应用回到前台');
        if (this.isRunning && !this.isPaused && this.backgroundStartTime > 0) {
          const backgroundDuration = Math.floor((Date.now() - this.backgroundStartTime) / 1000);
          if (backgroundDuration > 1) {
            console.log(`后台运行了${backgroundDuration}秒`);
            this.handleTimeJump(backgroundDuration);
          }
          this.backgroundStartTime = 0;
        }
      });
    },

    // 页面显示处理
    handlePageShow() {
      console.log('页面显示，恢复计时器');
      if (this.backgroundStartTime > 0) {
        const backgroundDuration = Math.floor((Date.now() - this.backgroundStartTime) / 1000);
        if (backgroundDuration > 1) {
          this.handleTimeJump(backgroundDuration);
        }
        this.backgroundStartTime = 0;
      }

      // 重新启动心跳检测
      if (!this.heartbeatTimer) {
        this.startHeartbeat();
      }
    },

    // 页面隐藏处理
    handlePageHide() {
      console.log('页面隐藏，记录时间');
      this.backgroundStartTime = Date.now();
    },

    // 初始化保活机制
    initKeepAlive() {
      // 添加保活回调
      keepAlive.addCallback((event, data) => {
        switch (event) {
          case 'heartbeat':
            // 心跳检测
            if (this.isRunning && !this.isPaused) {
              console.log('保活心跳 - 计时器运行中');
            }
            break;
          case 'timeJump':
            // 时间跳跃处理
            if (this.isRunning && !this.isPaused && data > 2) {
              console.log(`保活检测到时间跳跃: ${data}秒`);
              this.handleTimeJump(data);
            }
            break;
          case 'appShow':
            // 应用显示
            if (this.isRunning && !this.isPaused) {
              this.handlePageShow();
            }
            break;
          case 'appHide':
            // 应用隐藏
            if (this.isRunning && !this.isPaused) {
              this.handlePageHide();
            }
            break;
        }
      });

      // 监听保活更新事件
      uni.$on('keepAliveUpdate', (data) => {
        // 可以在这里添加一些轻量级的界面更新
        if (this.isRunning && !this.isPaused) {
          // 更新最后活动时间显示等
        }
      });
    },

    // 启动保活（在开始计时时调用）
    startAppKeepAlive() {
      console.log('启动应用保活机制');
      keepAlive.start();

      uni.showToast({
        title: '已启动保活机制',
        icon: 'success',
        duration: 1500
      });
    },

    // 停止保活（在停止计时时调用）
    stopAppKeepAlive() {
      console.log('停止应用保活机制');
      keepAlive.stop();
    },
  }
}
</script>

<style scoped>
.page-container {
  min-height: calc(100vh - 70rpx);
  padding: 5rpx;
  box-sizing: border-box;
  overflow-x: hidden;
  width: 100vw;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.part1,.part2{
  position:relative;
  width:100%;
}
.part2{
  padding-bottom:20rpx;
  min-height:88rpx;
}


/* 小时图标区域 */
.hour-icons {
  position: absolute;
  top: 9%;
  left: -5rpx;

  display: flex;
  gap: 10rpx;
  z-index: 10;
}

.hour-icon {
  width: 30rpx;
  height: 30rpx;
  border-radius: 50%;
  border: 2rpx solid #393C98;
  position: relative;
  overflow: hidden;
}

.hour-icon.filled {
  background: #393C98;
}

.hour-icon.partial {
  background: linear-gradient(to top, #393C98 var(--progress), transparent var(--progress));
}

/* 主计时器区域 */
.clock-container {
  position: relative;
  margin: 0rpx auto 0;
  background: transparent;
  overflow: visible;
  width: 100%;
  box-sizing: border-box;
  width: 640rpx;
  height: 640rpx;
}

.touch-tip{
  position:absolute;
  top: 80rpx;
  right: 20rpx;
  color: #393C98;
  font-size: 24rpx;
  line-height: 1.1;
  z-index: 100;
}

/* 进度圆环 */
.progress-ring {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
  width: 640rpx;
  height: 640rpx;
}

.ring-segment {
  width: 6rpx;
  height: 20rpx;
  background: rgb(187, 187, 187);
  border-radius: 3rpx;
  transition: background-color 0.3s ease;
  opacity: 0.3;
}

.ring-segment.active {
  background: rgb(57, 60, 152);
  opacity: 0.5;
}

/* 第一圈圆环段 */
.ring-segment.first-circle {
  z-index: 1;
}

/* 第二圈圆环段 - 叠加在第一圈上 */
.ring-segment.second-circle {
  z-index: 2;
}

.ring-segment.second-circle.active {
  background: rgb(57, 60, 152 ); /* 第二圈稍微透明一些，产生叠加效果 */
  opacity: 0.7;
}

/* 倒计时已过去的时间段 - 隐藏 */
.ring-segment.passed {
  opacity: 0;
  visibility: hidden;
}

/* 倒计时已过去的时间段 - 显示但样式不同 */
.ring-segment.passed-visible {
  background: rgba(200, 200, 200, 0.3); /* 灰色半透明 */
  opacity: 0.6;
}

/* 打点位置在已过去时间段中的可见样式 */
.ring-segment.timestamp-visible {
  opacity: 1 !important;
  visibility: visible !important;
  background: #ff6b6b !important; /* 打点标记颜色 */
}

/* 打点标记样式增强 */
.ring-segment.timestamp {
  background: #ff6b6b;
  opacity: 1;
  z-index: 10;
}

.ring-segment.highlighted {
  background: #ff6b6b;
}

.ring-segment.timestamp {
  background: #ff6b6b !important;
  width: 8rpx;
  height: 28rpx;
  box-shadow: 0 0 12rpx rgba(255, 107, 107, 0.8);
  border-radius: 4rpx;
}

/* 打点标记 */
.dot-marks {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 3;
  width: 640rpx;
  height: 640rpx;
}

.dot-mark {
  width: 24rpx;
  height: 24rpx;
  background: #ff6b6b;
  border: 4rpx solid #fff;
  border-radius: 50%;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
}

.clock-dial {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 2;
  background: transparent;
  width: 640rpx;
  height: 640rpx;
}

/* 当前时间显示 */
.current-time {
  position: absolute;
  left: 40rpx;
  top: 5%;
  transform: translate(-50%, -50%);
  color: #666;
  font-size: 32rpx;
  font-weight: normal;
  z-index: 10;
  letter-spacing: 2rpx;
}

/* 计时器时间显示 */
.clock-time {
  position: absolute;
  left: 50%;
  top: 48%;
  transform: translate(-50%, -50%);
  color: #393C98;
  font-size: 48rpx;
  font-weight: bold;
  z-index: 10;
  letter-spacing: 4rpx;
}

/* 刻度样式 */
.clock-tick {
  background: #393C98;
  border-radius: 2px;
}

/* 数字样式 */
.clock-num {
  color: #393C98;
  font-weight: bold;
  text-align: center;
  height: 60rpx;
  width: 60rpx;
  display: flex;
  flex-direction: row;
  justify-items: center;
  align-items: center;
  position: absolute;
  left: 50%;
  top: 54rpx;
  margin-left: -30rpx;
}

.clock-num .nn{
  width: 100%;
  height: 100%;
  font-size: 40rpx;
  line-height: 60rpx;
}

/* 垂直拖动条 */
.drag-slider-container {
  position: absolute;
  right: 20rpx;
  top: 150rpx;
  overflow: hidden;
  width:50rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 15;
  border-top:2rpx solid #999;
  padding-bottom:25rpx;
}
.drag-slider-container::after{
  content:"";
  position: absolute;
  width:100%;
  height:5rpx;
  top:0rpx;
  left:0;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0));
}

.drag-slider-track {
  position: relative;
  width: 8rpx;
  height: 400rpx;
  background: #e5e5e5;
  border-radius: 4rpx;

}

.drag-slider-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #f0f0f0;
}

.drag-slider-progress {
  display:none;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  background: linear-gradient(to bottom, #34c759, #393C98);
  border-radius: 4rpx;
  transition: height 0.1s ease;
}

.drag-slider-handle {
  position: absolute;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
  width: 40rpx;
  height: 40rpx;
  background: #fff;
  border: 3rpx solid #393C98;
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
  cursor: pointer;
  transition: all 0.2s ease;
}
.drag-slider-handle::before{
  content:"";
  display: block;
  background:#393C98;
  width:8rpx;
  height:400rpx;
 
  position: absolute;
  bottom: 40rpx;
  left: 50%;
  transform: translate(-50%, 0);
}

.drag-slider-handle:active {
  transform: translateX(-50%) translateY(-50%) scale(1.2);
}


/* 打点按钮 */
.dot-btn {
  position: absolute;
  left: 50%;
  top: 60%;
  transform: translateX(-50%);
  background: #ff6b6b;
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
  z-index: 15;
  cursor: pointer;
  transition: all 0.3s ease;
}

.dot-btn:active {
  transform: translateX(-50%) scale(0.95);
}

/* 操作按钮区 */
.clock-actions {
  display: flex;
  justify-content: center;
  margin-top: 10rpx;
  gap: 30rpx;
  padding: 0 40rpx;
}

.clock-btn {
  background: #393C98;
  color: #fff;
  font-size: 30rpx;
  border-radius: 50rpx;
  padding: 2rpx 48rpx;
  border: none;
  outline: none;
  box-shadow: 0 4rpx 12rpx rgba(57, 60, 152, 0.3);
  transition: all 0.3s ease;
  min-width: 160rpx;
}

.clock-btn:active {
  transform: scale(0.95);
}

.pause-btn {
  background: #ff9500;
  margin:0;
}

.resume-btn {
  background: #34c759;
  margin:0;
}

.end-btn {
  background: #8B648B;
  margin:0;
}

.reset-btn {
  background: #666;
   position: absolute;
  left: 50%;
  top: 57%;
  transform: translateX(-50%);
  padding:10rpx 20rpx;
  line-height:1.2em;
  font-size: 24rpx;
}

/* 动态圆环控制 */
.ring-control {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 40rpx;
  gap: 20rpx;
}

.control-btn {
  background: #f0f0f0;
  color: #666;
  font-size: 28rpx;
  border-radius: 40rpx;
  padding: 16rpx 32rpx;
  border: 2rpx solid #e0e0e0;
  transition: all 0.3s ease;
}

.control-btn.active {
  background: #393C98;
  color: white;
  border-color: #393C98;
}

.control-btn:active {
  transform: scale(0.95);
}

.control-desc {
  font-size: 24rpx;
  color: #999;
  text-align: center;
}

/* 音频测试按钮 */
.audio-test {
  display: flex;
  justify-content: center;
  gap: 20rpx;
  margin-top: 20rpx;
}

.test-btn {
  background: #ff6b6b;
  color: white;
  font-size: 28rpx;
  border-radius: 40rpx;
  padding: 16rpx 32rpx;
  border: none;
  transition: all 0.3s ease;
}

.test-btn:active {
  transform: scale(0.95);
  background: #ff5252;
}

.test-btn.stop-btn {
  background: #666;
}

.test-btn.stop-btn:active {
  background: #555;
}

/* 打点列表 */
.dot-list {

  background: white;
  border-radius: 20rpx;
  padding: 0 40rpx;
}

.dot-list-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
  display: flex;
  justify-content:space-between;
  align-items: center;
  gap: 20rpx;
  position: absolute;
  top:-50rpx;
}

.toggle-segments-btn {
  background: #393C98;
  color: white;
  font-size: 24rpx;
  border-radius: 30rpx;
  margin:0;
  border: none;
  transition: all 0.3s ease;
}

.toggle-segments-btn:active {
  transform: scale(0.95);
  background: #2d2f7a;
}

.dot-items {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  max-height:180rpx;
  overflow: scroll;
}

.dot-item {
  background: #f5f5f5;
  border: 2rpx solid #e0e0e0;
  border-radius: 15rpx;
  padding: 10rpx;
  min-width: 120rpx;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size:14rpx
}

.dot-item.selected {
  background: #393C98;
  border-color: #393C98;
  color: white;
}

.dot-item:active {
  transform: scale(0.95);
}

.dot-label {
  display: none;
  font-size: 24rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.dot-time {
  display: block;
  font-size: 20rpx;
  opacity: 0.8;
}
</style>
