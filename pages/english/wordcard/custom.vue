<template>
  <view class="container">
    <view class="header">
      <text class="title">自定义卡片</text>
    </view>

    <view class="content">
      <view class="input-section">
        <text class="section-title">输入单词</text>
        <text class="section-desc">请输入英文单词，多个单词请用逗号分隔（不支持中文）</text>

        <textarea
          class="input-area"
          v-model="inputWords"
          placeholder="例如：apple,banana,orange"
        ></textarea>

        <!-- 学习模式选择器已移除，使用默认的英文到中文模式 -->

        <button
          class="generate-btn"
          @click="generateCards"
          :disabled="!canGenerate"
        >生成卡片组</button>
      </view>

      <view class="tips-section">
        <text class="tips-title">使用提示：</text>
        <text class="tips-content">1. 只支持英文单词，不支持中文字符</text>
        <text class="tips-content">2. 单词之间用英文逗号(,)分隔</text>
        <text class="tips-content">3. 最多可输入50个单词</text>
        <text class="tips-content">4. 生成卡片后，可以通过滑动切换单词</text>
        <text class="tips-content">5. 点击卡片可以翻转查看释义</text>
      </view>
    </view>

    <view class="loading-mask" v-if="loading">
      <view class="loading-content">
        <text class="loading-text">正在生成卡片...</text>
      </view>
    </view>
  </view>
</template>

<script>
// 这些导入在学习页面使用，这里不需要
// import translator from '@/utils/translator.js';
// import customCardStorage from '@/utils/CustomCardStorage.js';

export default {
  data() {
    return {
      inputWords: '',
      loading: false
    }
  },
  computed: {
    canGenerate() {
      return this.inputWords.trim().length > 0;
    }
  },
  methods: {
    // 检查文本是否包含中文字符
    containsChinese(text) {
      // 使用正则表达式检查是否包含中文字符
      const chineseRegex = /[\u4e00-\u9fa5]/;
      return chineseRegex.test(text);
    },

    // 生成卡片
    async generateCards() {
      if (!this.canGenerate) {
        uni.showToast({
          title: '请输入单词',
          icon: 'none'
        });
        return;
      }

      try {
        this.loading = true;

        // 检查输入是否包含中文
        if (this.containsChinese(this.inputWords)) {
          uni.showToast({
            title: '请输入英文单词，不支持中文',
            icon: 'none'
          });
          this.loading = false;
          return;
        }

        // 分割输入文本，处理逗号、空格和换行符
        const words = this.inputWords
          .split(/[,，\n\s]+/)
          .map(word => word.trim())
          .filter(word => word);

        if (words.length === 0) {
          uni.showToast({
            title: '请输入有效的单词',
            icon: 'none'
          });
          this.loading = false;
          return;
        }

        if (words.length > 50) {
          uni.showToast({
            title: '单词数量不能超过50个',
            icon: 'none'
          });
          this.loading = false;
          return;
        }

        // 显示加载提示
        uni.showLoading({
          title: '正在准备卡片...',
          mask: true
        });

        try {
          // 不在这里获取翻译，而是在 learn 页面获取翻译
          console.log('准备跳转到学习页面，单词数量:', words.length);
        } catch (error) {
          console.error('准备卡片失败:', error);
        } finally {
          uni.hideLoading();
        }

        // 将单词列表转换为字符串
        const wordsStr = encodeURIComponent(JSON.stringify(words));

        // 跳转到学习页面，使用默认的英文到中文模式
        uni.navigateTo({
          url: `/pages/english/wordcard/learn?words=${wordsStr}&isCustom=true`
        });
      } catch (error) {
        console.error('生成卡片失败:', error);
        uni.showToast({
          title: '生成卡片失败，请重试',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    }
  }
}
</script>

<style>
.container {
  padding: 30rpx;
  min-height: 100vh;
  background-color: #f8f8f8;
}

.header {
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.content {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.section-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
}

.input-area {
  width: 100%;
  height: 300rpx;
  padding: 20rpx;
  background-color: #f9f9f9;
  border-radius: 10rpx;
  margin-bottom: 30rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.options-section {
  margin-bottom: 40rpx;
}

.option-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.option-label {
  font-size: 28rpx;
  color: #333;
  margin-right: 20rpx;
}

.mode-selector {
  display: flex;
  flex: 1;
  background-color: #f0f0f0;
  border-radius: 10rpx;
  overflow: hidden;
}

.mode-item {
  flex: 1;
  padding: 15rpx 0;
  text-align: center;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s;
}

.mode-item.active {
  background-color: #393C98;
  color: #fff;
}

.generate-btn {
  background-color: #393C98;
  color: #fff;
  font-size: 32rpx;
  padding: 20rpx 0;
  border-radius: 10rpx;
  margin-bottom: 40rpx;
}

.generate-btn[disabled] {
  background-color: #cccccc;
  color: #999999;
}

.tips-section {
  margin-top: 40rpx;
  padding-top: 30rpx;
  border-top: 1px solid #f0f0f0;
}

.tips-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.tips-content {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.loading-content {
  background-color: #fff;
  padding: 40rpx;
  border-radius: 10rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.loading-text {
  font-size: 28rpx;
  color: #333;
}
</style>
