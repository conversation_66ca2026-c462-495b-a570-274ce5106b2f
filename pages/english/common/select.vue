<template>
  <view class="container">
    <!-- 顶部标题区域 -->
		
    <view class="header">
      <view class="header-content">
        <view class="title">{{ pageTitle }}</view>
        <view class="subtitle">选择教材和单元开始{{ actionText }}</view>
      </view>
    </view>
    
    <!-- 教材选择区域 -->
    <view class="textbook-selector">
      <view class="section-title">选择教材</view>
      <view class="textbook-picker">
        <picker 
          @change="onTextbookChange" 
          :value="selectedTextbookIndex" 
          :range="textbooks"
          range-key="name"
        >
          <view class="picker-content">
            <text>{{ textbooks[selectedTextbookIndex].name }}</text>
            <text class="picker-arrow">›</text>
          </view>
        </picker>
      </view>
    </view>
    
    <view class="grade-selector">
      <view class="section-title">选择年级</view>
      <view class="grade-list">
        <view 
          v-for="(grade, key) in grades" 
          :key="key"
          :class="['grade-item', selectedGrade === key ? 'active' : '']"
          @click="selectGrade(key)"
        >
          {{ grade.name }}
        </view>
      </view>
    </view>
    
    <view class="semester-selector" v-if="selectedGrade">
      <view class="section-title">选择学期</view>
      <view class="semester-list">
        <view 
          v-for="(semester, key) in semesters" 
          :key="key"
          :class="['semester-item', selectedSemester === key ? 'active' : '']"
          @click="selectSemester(key)"
        >
          {{ semester.name }}
        </view>
      </view>
    </view>
    
    <view class="unit-selector" v-if="selectedGrade && selectedSemester">
      <view class="section-title">选择单元 (可多选)</view>
      <view class="unit-list">
        <view 
          v-for="(unit, key) in units" 
          :key="key"
          :class="['unit-item', selectedUnits.includes(key) ? 'active' : '']"
          @click="toggleUnit(key)"
        >
          {{ unit.name }}
        </view>
      </view>
    </view>
    
    <view class="start-btn-wrapper" v-if="selectedGrade && selectedSemester && selectedUnits.length > 0">
      <button class="start-btn" @click="handleStart">开始{{ actionText }}</button>
    </view>
  </view>
</template>

<script>
// 修改导入方式
import { gradeData } from '@/static/data/index.js';

export default {
  data() {
    return {
      textbooks: [
        { id: 'wy1', name: '外研版一年级起点' }
      ],
      selectedTextbookIndex: 0,
      grades: {
        grade1: { name: '一年级' },
        grade2: { name: '二年级' },
        grade3: { name: '三年级' },
        grade4: { name: '四年级' },
        grade5: { name: '五年级' },
        grade6: { name: '六年级' },
      },
      semesters: {
        semester1: { name: '上学期' },
        semester2: { name: '下学期' }
      },
      units: {},
      selectedGrade: '',
      selectedSemester: '',
      selectedUnits: [],
      pageTitle: '',
      actionText: '',
      targetPage: '',
      dataLoaded: false,
      currentGradeData: null,
    }
  },
  onLoad(options) {
    // 根据传入的type设置页面标题和动作文本
    const { type } = options;
    switch(type) {
      case 'wordcard':
        this.pageTitle = '单词卡片';
        this.actionText = '学习';
        this.targetPage = '/pages/english/wordcard/learn';
        break;
      case 'test':
        this.pageTitle = '英语测试';
        this.actionText = '测试';
        this.targetPage = '/pages/english/test/test';
        break;
      case 'dictation':
        this.pageTitle = '英语听写';
        this.actionText = '听写';
        this.targetPage = '/pages/dictation/index';
        break;
    }
  },
  onShow() {
    // #ifdef MP-WEIXIN
    // 在小程序环境下，确保数据已加载
    if (!this.dataLoaded) {
      this.checkWordData();
    }
    // #endif
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    
    onTextbookChange(e) {
      this.selectedTextbookIndex = e.detail.value;
      this.selectedGrade = '';
      this.selectedSemester = '';
      this.selectedUnits = [];
      const gradeData = this.getGradeData(this.selectedGrade)
      if (gradeData) {
        // 使用 gradeData 替代原来的 wordData[grade]
        // ...
      }
    },
    
    async selectGrade(grade) {
      try {
        this.selectedGrade = grade;
        this.selectedSemester = '';
        this.selectedUnits = [];
        this.units = {};

        // 直接使用 getGradeData 获取年级数据
        const gradeData = this.getGradeData(grade);
        if (!gradeData) {
          throw new Error('Failed to load grade data');
        }
        
        this.currentGradeData = gradeData;
        this.dataLoaded = true;
        
      } catch (error) {
        console.error('Error loading grade data:', error);
        uni.showToast({
          title: '加载年级数据失败',
          icon: 'none'
        });
      }
    },
    
    selectSemester(semester) {
      try {
        this.selectedSemester = semester;
        this.selectedUnits = [];
        this.units = {};

        if (!this.selectedGrade || !this.currentGradeData) {
          uni.showToast({
            title: '请先选择年级',
            icon: 'none'
          });
          return;
        }

        // 直接从 currentGradeData 获取学期数据
        const semesterData = this.currentGradeData[semester];
        
        if (semesterData) {
          for (let moduleKey in semesterData) {
            if (semesterData.hasOwnProperty(moduleKey)) {
              const moduleNumber = moduleKey.replace('module', '');
              this.units[moduleKey] = { 
                name: `Unit ${moduleNumber}`,
                count: Array.isArray(semesterData[moduleKey]) ? semesterData[moduleKey].length : 0
              };
            }
          }
        } else {
          console.log('No data found for:', this.selectedGrade, semester);
          uni.showToast({
            title: '暂无该学期数据',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('Error in selectSemester:', error);
        uni.showToast({
          title: '加载单元数据失败',
          icon: 'none'
        });
      }
    },
    
    toggleUnit(unit) {
      const index = this.selectedUnits.indexOf(unit);
      if (index === -1) {
        this.selectedUnits.push(unit);
      } else {
        this.selectedUnits.splice(index, 1);
      }
    },
    
    handleStart() {
      try {
        if (this.selectedUnits.length === 0) {
          uni.showToast({
            title: '请至少选择一个单元',
            icon: 'none'
          });
          return;
        }

        // 构建查询参数对象
        const params = {
          grade: this.selectedGrade,
          semester: this.selectedSemester,
          units: this.selectedUnits, // 不要在这里 JSON.stringify
          mode: this.selectedMode || 'en2zh'
        };

        // 使用 navigateTo 跳转到目标页面，手动处理 units 参数
        const units = encodeURIComponent(JSON.stringify(this.selectedUnits));
        const otherParams = Object.entries(params)
          .filter(([key]) => key !== 'units')
          .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
          .join('&');

        uni.navigateTo({
          url: `${this.targetPage}?${otherParams}&units=${units}`,
          fail: (err) => {
            console.error('Navigation failed:', err);
            uni.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      } catch (error) {
        console.error('Error in handleStart:', error);
        uni.showToast({
          title: '操作失败',
          icon: 'none'
        });
      }
    },
    
    // 修改获取年级数据的方法
    getGradeData(grade) {
      if (!grade) return null;
      try {
        const data = gradeData[grade];
        if (!data) {
          console.error('No data found for grade:', grade);
          return null;
        }
        return data;
      } catch (error) {
        console.error('Error getting grade data:', error);
        return null;
      }
    },

    // 修改数据检查方法
    checkWordData() {
      // 只在选择了年级后才检查数据
      if (!this.selectedGrade) {
        this.dataLoaded = true; // 没有选择年级时也标记为已加载
        return;
      }

      const gradeData = this.getGradeData(this.selectedGrade);
      if (!gradeData) {
        setTimeout(() => {
          console.log('Retrying to load wordData...');
          this.checkWordData();
        }, 500);
      } else {
        console.log('WordData loaded successfully');
        this.currentGradeData = gradeData;
        this.dataLoaded = true;
      }
    },
  }
}
</script>

<style lang="scss">
@import './select.scss';
</style> 