<template>
	<view class="container">
		<!-- 顶部标题区域 -->
	
		
		<!-- 目录卡片列表 -->
		<view class="catalog-list">
			<view class="menu-item">
				<view class="catalog-item" v-for="(item, index) in catalogList" :key="index" @click="navigateTo(item.url)" :class="{ 'disabled': !item.state }">
					<!-- 图标区域 -->
					<view class="icon-wrapper" :style="{ backgroundColor: item.bgColor }">
						<text class="icon-text">{{ item.title.substr(0, 1) }}</text>
					</view>
					
					<!-- 内容区域 -->
					<view class="content">
						<text class="item-title">{{ item.title }}</text>
						<text class="item-desc">{{ item.description }}</text>
					</view>
					
					<!-- 右侧箭头 -->
					<text class="arrow">›</text>
				</view>
			</view>
				
			
		</view>
		<!-- 添加邀请码入口 -->
		<view class="invite-entry" @click="goToInvite">
			<text class="invite-text">{{ isVip ? 'VIP用户' : '邀请码' }}</text>
		</view>
	</view>
</template>

<script>
	import { userAuth } from '@/utils/UserAuth.js';

	export default {
		data() {
			return {
				catalogList: [
					{
						title: '听写助手 ',
						description: '自定义听写(中/英)',
						url: '/pages/dictation/list',
						bgColor: 'rgba(57, 60, 152, 0.1)',
						state:true
					
					},
					{
						title: '英语助手 ',
						description: '单词卡片快闪(可自定义)',
						url: '/pages/english/index',
						bgColor: 'rgba(139, 100, 139, 0.1)',
						state:true
					}
					,
					{
						title: '计时器',
						description: '正/倒计时/打点',
						url: '/pages/clock/index',
						bgColor: 'rgba(57, 60, 152, 0.1)',
						state: true
					}
				],
				dictationDisabled: false,
				isVip: false
			}
		},
		async onShow() {
			//清理 vip
			// this.clearVip();
			// 初始化用户信息
			await userAuth.init();
			// 检查听写功能状态
			await this.checkDictationStatus();
			// 检查VIP状态
			this.checkVipStatus();
		},
		methods: {
			//清理 vip
			clearVip() {
				userAuth.clearVipStatus();
				userAuth.clearUsageCount();
			},
			// 检查听写功能状态
			async checkDictationStatus() {
				try {
					const status = await userAuth.checkFeaturePermission('dictation');
					console.log('听写功能状态:', status);
					this.dictationDisabled = !status.allowed;
					
					this.catalogList[0].state = !this.dictationDisabled;
					// 强制更新视图
					if (this.dictationDisabled) {
						this.$forceUpdate();
					}
				} catch (error) {
					console.error('检查听写功能状态失败:', error);
				}
			},
			
			// 修改跳转到听写页面的方法
			async goDictation() {
				const result = await userAuth.checkFeaturePermission('dictation');
				console.log('听写状态', result);
				if (!result.allowed) {
					uni.showToast({
						title: result.message,
						icon: 'none',
						duration: 3000
					});
					return;
				}
				
				// 跳转到听写页面
				uni.navigateTo({
					url: '/pages/dictation/list'
				});
			},
			navigateTo(url) {
				// 如果是听写功能，需要先验证权限
				if (url.includes('dictation')) {
					this.goDictation();
					return;
				}
				
				// 其他功能直接跳转
				uni.reLaunch({
					url: url,
					fail: (err) => {
						console.error('reLaunch 失败:', err);
					}
				});
			},
			// 检查VIP状态
			checkVipStatus() {
				this.isVip = userAuth.isVipUser();
			},
			
			// 跳转到邀请码页面
			goToInvite() {
				if (this.isVip) {
					uni.showToast({
						title: '您已是VIP用户',
						icon: 'none'
					});
					return;
				}
				
				uni.navigateTo({
					url: '/pages/invite/index'
				});
			}
		}
	}
</script>

<style>
	.container {
		min-height: 100vh;
		background-color: #f8f8f8;
		padding: 20rpx;
		box-sizing: border-box;
	}
	.title {
		font-size: 48rpx;
		font-weight: 600;
		color: #393C98;
	}

	.catalog-list {
		padding: 20rpx 0;
	}

	.catalog-item {
		display: flex;
		align-items: center;
		background-color: #ffffff;
		border-radius: 24rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
		-webkit-tap-highlight-color: transparent;
		outline: none;
		
	}
	
	.icon-wrapper {
		width: 100rpx;
		height: 100rpx;
		border-radius: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.icon-text {
		font-size: 36rpx;
		font-weight: bold;
		color: #393C98;
	}
	
	.content {
		flex: 1;
		padding: 0 30rpx;
	}
	
	.item-title {
		font-size: 36rpx;
		font-weight: 500;
		color: #333;
		margin-bottom: 8rpx;
	}
	
	.item-desc {
		font-size: 28rpx;
		color: #666;
	}
	
	.arrow {
		font-size: 48rpx;
		color: #393C98;
		font-weight: 300;
	}
	
	.catalog-item:active {
		transform: scale(0.98);
		transition: transform 0.2s;
	}

	/* 添加导航器悬停样式 */
	.navigator-hover {
		opacity: 1;
	}
	.uni-navigator {
		-webkit-tap-highlight-color: transparent;
		outline: none;
		background:red
	}

	.disabled {
		opacity: 0.5;
		background-color: #f5f5f5 !important;
	}

	.disabled .icon-text,
	.disabled .item-title,
	.disabled .item-desc,
	.disabled .arrow {
		color: #999 !important;
	}

	.invite-entry {
		position: fixed;
		bottom: 40rpx;
		right: 0rpx;
		padding: 16rpx 32rpx;
		border-radius: 32rpx;
		z-index: 100;
	}

	.invite-text {
		color: #ccc;
		font-size: 28rpx;
	}

	.vip-tag {
		background-color: #8B648B;
	}
</style>
