{
    "name" : "七宝学习助手",
    "appid" : "__UNI__B890FB6",
    "description" : "小学生学习助手",
    "versionName" : "1.0.0",
    "versionCode" : "100",
    "transformPx" : false,
    /* 5+App特有相关 */
    "app-plus" : {
        "name" : "七宝学习助手",
        "usingComponents" : true,
        "nvueStyleCompiler" : "uni-app",
        "compilerVersion" : 3,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        /* 模块配置 */
        "modules" : {
            "Speech" : {},
            "MT-TTS-Speech" : {},
            "xwq-tts-speaker" : {}
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "packagename" : "com.qibao.study",
                "keystore" : "",
                "password" : "",
                "aliasname" : "",
                "schemes" : "",
                "permissions" : [
                    "<uses-permission android:name=\"android.permission.INTERNET\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.DISABLE_KEYGUARD\"/>",
                    "<uses-permission android:name=\"android.permission.SYSTEM_ALERT_WINDOW\"/>",
                    "<uses-permission android:name=\"android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS\"/>",
                    "<queries><intent><action android:name=\"android.intent.action.TTS_SERVICE\" /></intent></queries>"
                ],
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a", "x86" ],
                "targetSdkVersion" : 30,
                "minSdkVersion" : 21
            },
            /* ios打包配置 */
            "ios" : {
                "appid" : "",
                "mobileprovision" : "",
                "password" : "",
                "p12" : "",
                "devices" : "universal",
                "frameworks" : [],
                "dSYMs" : false
            },
            /* SDK配置 */
            "sdkConfigs" : {
                "speech" : {
                    "ifly" : {}
                }
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            },
            "splashscreen" : {
                "android" : {
                    "hdpi" : "unpackage/res/splash/splash.9.png",
                    "xhdpi" : "unpackage/res/splash/splash.9.png",
                    "xxhdpi" : "unpackage/res/splash/splash.9.png"
                },
                "ios" : {
                    "iphone" : {
                        "portrait-896h@3x" : "unpackage/res/splash/1242x2688.png",
                        "portrait-896h@2x" : "unpackage/res/splash/828x1792.png",
                        "iphonex" : "unpackage/res/splash/1125x2436.png",
                        "retina55" : "unpackage/res/splash/1242x2208.png",
                        "retina47" : "unpackage/res/splash/750x1334.png",
                        "retina40" : "unpackage/res/splash/640x1136.png",
                        "retina35" : "unpackage/res/splash/640x960.png"
                    }
                }
            },
            "plugins" : {
                "MT-TTS-Speech" : {
                    "version" : "1.0.0",
                    "provider" : "MT-TTS-Speech"
                },
                "xwq-tts-speaker" : {
                    "version" : "1.1.1",
                    "provider" : "xwq-tts-speaker"
                }
            }
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "wxefcf8fa65f7c0808",
        "setting" : {
            "urlCheck" : false,
            "es6" : true,
            "postcss" : true,
            "minified" : true,
            "packNpmManually" : true,
            "packNpmRelationList" : [
                {
                    "packageJsonPath" : "./package.json",
                    "miniprogramNpmDistDir" : "./dist"
                }
            ],
            "enhance" : true,
            "optimizeWXSS" : true,
            "minifyWXSS" : true,
            "minifyWXML" : true,
            "uglifyFileName" : false,
            "checkSiteMap" : false
        },
        "usingComponents" : true,
        "lazyCodeLoading" : "requiredComponents",
        "permission" : {
            "scope.record" : {
                "desc" : "需要使用语音功能"
            },
            "scope.userLocation" : {
                "desc" : "你的位置信息将用于小程序位置接口的效果展示"
            },
            "scope.userInfo" : {
                "desc" : "用于记录您的使用情况"
            }
        },
        "requiredPrivateInfos" : [ "getLocation", "chooseLocation", "chooseAddress" ],
        "requiredBackgroundModes" : [ "audio" ],
        "plugins" : {},
        "resizable" : true,
        "functionalPages" : false,
        "darkmode" : false,
        "themeLocation" : "theme.json",
        "optimization" : {
            "subPackages" : true
        }
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "uniStatistics" : {
        "enable" : false
    },
    "vueVersion" : "3"
}
