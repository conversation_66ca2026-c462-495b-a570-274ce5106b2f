/**
 * 自定义卡片存储工具
 * 用于存储自定义卡片的单词和翻译结果
 */

// 存储键名
const STORAGE_KEY = 'custom_card_data';

/**
 * 保存自定义卡片数据
 * @param {Array} words 英文单词数组
 * @param {Array} translations 中文翻译数组
 */
export function saveCustomCardData(words, translations) {
  try {
    console.log('开始保存自定义卡片数据:', {
      wordsCount: words ? words.length : 0,
      translationsCount: translations ? translations.length : 0,
      words: words,
      translations: translations
    });

    if (!Array.isArray(words) || !Array.isArray(translations) || words.length !== translations.length) {
      console.error('保存自定义卡片数据失败：数据格式不正确', {
        isWordsArray: Array.isArray(words),
        isTranslationsArray: Array.isArray(translations),
        wordsLength: words ? words.length : 0,
        translationsLength: translations ? translations.length : 0
      });
      return false;
    }

    // 创建单词和翻译的映射
    const cardData = {
      words: words,
      translations: translations,
      timestamp: Date.now()
    };

    // 将数据转换为 JSON 字符串
    const jsonData = JSON.stringify(cardData);
    console.log('将要保存的 JSON 数据:', jsonData);

    // 保存到本地存储
    uni.setStorageSync(STORAGE_KEY, jsonData);

    // 验证数据是否保存成功
    const savedData = uni.getStorageSync(STORAGE_KEY);
    if (savedData === jsonData) {
      console.log('自定义卡片数据保存成功');
      return true;
    } else {
      console.error('自定义卡片数据保存失败：保存的数据与原始数据不一致');
      return false;
    }
  } catch (error) {
    console.error('保存自定义卡片数据失败：', error);
    return false;
  }
}

/**
 * 获取自定义卡片数据
 * @returns {Object|null} 自定义卡片数据，包含 words 和 translations 数组
 */
export function getCustomCardData() {
  try {
    console.log('开始从本地存储中获取自定义卡片数据');

    const data = uni.getStorageSync(STORAGE_KEY);
    console.log('从本地存储中获取的原始数据:', data);

    if (!data) {
      console.warn('本地存储中没有自定义卡片数据');
      return null;
    }

    const cardData = JSON.parse(data);
    console.log('解析后的卡片数据:', cardData);

    if (!cardData.words || !cardData.translations || cardData.words.length !== cardData.translations.length) {
      console.error('获取自定义卡片数据失败：数据格式不正确', {
        hasWords: !!cardData.words,
        hasTranslations: !!cardData.translations,
        wordsLength: cardData.words ? cardData.words.length : 0,
        translationsLength: cardData.translations ? cardData.translations.length : 0
      });
      return null;
    }

    console.log('成功获取自定义卡片数据:', {
      wordsCount: cardData.words.length,
      translationsCount: cardData.translations.length,
      timestamp: cardData.timestamp
    });

    return cardData;
  } catch (error) {
    console.error('获取自定义卡片数据失败：', error);
    return null;
  }
}

/**
 * 清除自定义卡片数据
 */
export function clearCustomCardData() {
  try {
    uni.removeStorageSync(STORAGE_KEY);
    console.log('自定义卡片数据已清除');
    return true;
  } catch (error) {
    console.error('清除自定义卡片数据失败：', error);
    return false;
  }
}

export default {
  saveCustomCardData,
  getCustomCardData,
  clearCustomCardData
};
