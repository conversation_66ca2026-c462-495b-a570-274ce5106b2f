<template>
	<view class="english-container">
		<view class="header">
			<text class="title">英语学习</text>
		</view>

		<view class="menu-list">
			<view class="menu-item" @click="navigateToWordCard">
				<text class="menu-title">校内单词卡片</text>
				<text class="menu-desc">教材配套单词卡片</text>
				<text class="arrow">›</text>
			</view>

			<view class="menu-item" @click="navigateToCustomCard">
				<text class="menu-title">自定义单词卡片</text>
				<text class="menu-desc">创建自定义单词卡片</text>
				<text class="arrow">›</text>
			</view>

			<view class="menu-item" @click="navigateToTest">
				<text class="menu-title">单词测试</text>
				<text class="menu-desc">测试你的单词掌握程度</text>
				<text class="arrow">›</text>
			</view>
			<!-- <view class="menu-item" @click="navigateToTrs">
				<text class="menu-title">翻译</text>
				<text class="menu-desc">单词翻译</text>
				<text class="arrow">›</text>
			</view> -->
		</view>
		<!-- 返回首页 -->
		<view class="back-btn" @click="goBack">
			<text>返回首页</text>
		</view>
	</view>
</template>

<script>
	// 添加新的年级数据引用
	import grade1 from '@/static/data/grade1.js'
	import grade2 from '@/static/data/grade2.js'
	import grade3 from '@/static/data/grade3.js'
	import grade4 from '@/static/data/grade4.js'
	import grade5 from '@/static/data/grade5.js'
	import grade6 from '@/static/data/grade6.js'
	import { speech } from '@/utils/speech.js';

	export default {
		data() {
			return {
				menuList: [
					{
						title: '单词学习',
						description: '通过卡片记忆单词',
						url: '/pages/english/common/select?type=learn',
						bgColor: 'rgba(57, 60, 152, 0.1)',
						icon: '📚'
					},
					{
						title: '单词测试',
						description: '检验单词掌握程度',
						url: '/pages/english/common/select?type=test',
						bgColor: 'rgba(139, 100, 139, 0.1)',
						icon: '✍️'
					}
				]
			}
		},
		methods: {
			// 添加获取年级数据的方法
			getGradeData(grade) {
				const gradeMap = {
					'grade1': grade1,
					'grade2': grade2,
					'grade3': grade3,
					'grade4': grade4,
					'grade5': grade5,
					'grade6': grade6
				}
				return gradeMap[grade]?.[grade]
			},
			navigateToWordCard() {
				uni.navigateTo({
					url: '/pages/english/common/select?type=wordcard'
				});
			},
			navigateToTest() {
				uni.navigateTo({
					url: '/pages/english/common/select?type=test'
				});
			},
			navigateToCustomCard() {
				uni.navigateTo({
					url: '/pages/english/wordcard/custom'
				});
			},
			navigateToTrs(){
				uni.navigateTo({
					url: '/pages/translator/tx-translator'
				});
			},
			goBack() {
				uni.reLaunch({
					url: '/pages/index/index'
				});
			},
			// 修改语音播放方法
			async playMenuTitle(title) {
				try {
					await speech.speak(title);  // 不需要指定 lang，会自动检测
				} catch (error) {
					console.error('播放失败:', error);
					uni.showToast({
						title: '播放失败',
						icon: 'none'
					});
				}
			},
			// 导航方法
			navigateTo(url) {
				uni.navigateTo({
					url: url,
					fail: (err) => {
						console.error('导航失败:', err);
						uni.showToast({
							title: '页面跳转失败',
							icon: 'none'
						});
					}
				});
			}
		}
	}
</script>

<style>
	.english-container {
		min-height: 100vh;
		background-color: #f8f8f8;
		padding: 30rpx;
	}



	.title {
		font-size: 48rpx;
		font-weight: 600;
		color: #393C98;
	}

	.menu-list {
		margin-bottom: 40rpx;
	}

	.menu-item {
		background-color: #ffffff;
		border-radius: 24rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
		display: flex;
		flex-direction: column;
	}

	.menu-title {
		font-size: 36rpx;
		font-weight: 500;
		color: #333;
		margin-bottom: 10rpx;
	}

	.menu-desc {
		font-size: 28rpx;
		color: #666;
	}

	.arrow {
		position: absolute;
		right: 50rpx;
		font-size: 48rpx;
		color: #393C98;
		font-weight: 300;
	}

	.menu-item:active {
		transform: scale(0.98);
		transition: transform 0.2s;
	}


</style>