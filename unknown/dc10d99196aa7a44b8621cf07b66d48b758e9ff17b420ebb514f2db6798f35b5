
/* 选择器通用样式 */
.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
}

.picker-content {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 16rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.picker-arrow {
  font-size: 36rpx;
  color: #393C98;
}

/* 列表项通用样式 */
.grade-list,
.semester-list,
.unit-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.grade-item,
.semester-item,
.unit-item {
  flex: 1;
  min-width: 200rpx;
  padding: 30rpx;
  background-color: #f0f0f0;
  border-radius: 16rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333;
  transition: all 0.3s;
}

.grade-item.active,
.semester-item.active,
.unit-item.active {
  background-color: #393C98;
  color: #fff;
}

/* 底部按钮通用样式 */
.start-btn-wrapper {
  margin-top: 60rpx;
  padding: 0 40rpx;
}

.start-btn {
  background-color: #393C98;
  color: #fff;
  border-radius: 50rpx;
  font-size: 32rpx;
  padding: 24rpx 0;
  width: 100%;
  text-align: center;
  box-shadow: 0 8rpx 16rpx rgba(57, 60, 152, 0.3);
}

/* 选择器组件通用样式 */
.textbook-selector,
.grade-selector,
.semester-selector,
.unit-selector,
.test-mode-selector {
  background-color: #fff;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
} 