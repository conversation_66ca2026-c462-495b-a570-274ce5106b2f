<template>
  <page-layout
    :show-mode-selector="!testing"
    :modes="testModes"
    :current-mode="selectedMode"
    :show-progress="testing"
    :current="currentIndex + 1"
    :total="questions.length"
    :show-controls="true"
    @back="handleBack"
    @switch-mode="switchMode"
  >
    <!-- 题目区域 -->
    <view class="question-area">
      <view class="question-card">
        <view :class="['question', selectedMode === 'spell' ? 'spell-mode' : '']">
          <template v-if="selectedMode === 'spell'">
            <view class="spell-chinese">{{ currentQuestion.chinese }}</view>
            <view class="spell-word" v-if="currentQuestion && currentQuestion.word && typeof currentQuestion.word === 'string'">
              <text>{{ currentQuestion.word.substring(0, currentQuestion.gapStart) }}</text>
              <text :class="['gap-text', {'show-answer': showAnswer}]">
                {{ currentQuestion.word.substring(currentQuestion.gapStart, currentQuestion.gapEnd) }}
              </text>
              <text>{{ currentQuestion.word.substring(currentQuestion.gapEnd) }}</text>
            </view>
            <!-- 处理 word 是对象的情况 -->
            <view class="spell-word" v-else-if="currentQuestion && currentQuestion.word && typeof currentQuestion.word === 'object' && currentQuestion.word.en">
              <text>{{ currentQuestion.word.en.substring(0, currentQuestion.gapStart) }}</text>
              <text :class="['gap-text', {'show-answer': showAnswer}]">
                {{ currentQuestion.word.en.substring(currentQuestion.gapStart, currentQuestion.gapEnd) }}
              </text>
              <text>{{ currentQuestion.word.en.substring(currentQuestion.gapEnd) }}</text>
            </view>
          </template>
          <template v-else>
            {{ currentQuestion.question }}
          </template>
        </view>

        <view :class="['options', selectedMode === 'spell' ? 'spell-mode' : '']">
          <view
            v-for="(option, index) in currentQuestion.options"
            :key="index"
            :class="['option-item', getOptionClass(index), {
              'shake-animation': wrongIndexes.includes(index)
            }]"
            @click="selectAnswer(index)"
          >
            {{ option }}
          </view>
        </view>
      </view>

      <!-- 添加遮罩层 -->
      <view class="mask" v-if="!testing">
        <view class="mask-text">请选择模式后<br>开始测试</view>
      </view>
    </view>

    <!-- 底部控制按钮 -->
    <template #controls>
      <button class="end-test-btn" @click="endTest" v-if="testing">结束测试</button>
      <button class="end-test-btn" @click="startTest" v-else>开始测试</button>
    </template>

    <!-- 结果弹窗 -->
    <view class="result-popup" v-if="showResult">
      <view class="result-content">
        <view class="result-header">
          <text class="result-title">测试结果</text>
          <text class="result-score">得分：{{ correctCount }}/{{ questions.length }}</text>
        </view>

        <scroll-view class="result-list" scroll-y>
          <!-- 已完成的题目 -->
          <view class="result-section">
            <view class="section-title">已完成题目</view>
            <view
              v-for="(item, index) in resultList"
              :key="index"
              :class="['result-item', {
                'correct': item.isCorrect,
                'wrong': !item.isCorrect || item.wrongAttempts > 0,
                'perfect': selectedMode === 'spell' && item.wrongAttempts === 0
              }]"
            >
              <view class="result-question">
                <template v-if="selectedMode === 'spell'">
                  <text class="word-text">{{ item.word }}</text>
                  <text class="word-chinese">{{ item.chinese }}</text>
                </template>
                <template v-else>
                  <text class="word-text">{{ item.question }}</text>
                  <text class="word-chinese">{{ item.chinese }}</text>
                </template>
              </view>
              <view class="result-answer">
                <text>正确答案：{{ item.correctAnswer }}</text>
                <text v-if="!item.isCorrect" class="wrong-answer">
                  选择答案：{{ item.selectedAnswer }}
                </text>
                <view v-if="selectedMode === 'spell' && item.wrongAttempts > 0"
                      :class="['attempts', item.wrongAttempts > 1 ? 'multiple' : '']">
                  尝试次数：{{ item.wrongAttempts + 1 }}
                </view>
                <!-- 错题标记按钮 -->
                <text
                  class="error-mark"
                  :class="{ 'active': isInErrorBook(item.word) }"
                  @tap="isInErrorBook(item.word) ? null : addToErrorBook(item.word)"
                  v-if="item.word"
                >错</text>
              </view>
            </view>
          </view>

          <!-- 未完成的题目 -->
          <view class="result-section" v-if="getUnfinishedQuestions().length > 0">
            <view class="section-title">未完成题目</view>
            <view
              v-for="(question, index) in getUnfinishedQuestions()"
              :key="index"
              class="result-item unfinished"
            >
              <view class="result-question">
                <template v-if="selectedMode === 'spell'">
                  <text class="word-text">{{ question.word }}</text>
                  <text class="word-chinese">{{ question.chinese }}</text>
                </template>
                <template v-else>
                  <text class="word-text">{{ question.question }}</text>
                  <text class="word-chinese">{{ question.chinese }}</text>
                </template>
              </view>
              <view class="result-answer">
                <text>正确答案：{{ selectedMode === 'spell' ?
                  question.word.substring(question.gapStart, question.gapEnd) :
                  question.correctAnswer }}</text>
                <!-- 错题标记按钮 -->
                <text
                  class="error-mark"
                  :class="{ 'active': isInErrorBook(question.word) }"
                  @tap="isInErrorBook(question.word) ? null : addToErrorBook(question.word)"
                  v-if="question.word"
                >错</text>
              </view>
            </view>
          </view>
        </scroll-view>

        <view class="result-actions">
          <button @click="retryTest">重新测试</button>
          <button @click="goDictation" class="dictation-btn">去听写</button>
          <button @click="goBack">返回</button>
        </view>
      </view>
    </view>
  </page-layout>
</template>

<script>
import PageLayout from '@/components/common/PageLayout.vue';
import { gradeData } from '@/static/data/index.js';
import errorBookManager from '@/utils/ErrorBookManager.js'; // 导入错题本管理工具
import translator from '@/utils/translationService.js'; // 导入整合的翻译服务
import customCardStorage from '@/utils/CustomCardStorage.js'; // 导入自定义卡片存储工具

export default {
  components: {
    PageLayout
  },
  data() {
    return {
      questions: [],
      currentIndex: 0,
      answered: false,
      selectedAnswer: -1,
      correctCount: 0,
      showResult: false,
      resultList: [],
      testing: false,
      mode: '',
      testModes: [
        { value: 'en2zh', label: '英-中' },
        { value: 'zh2en', label: '中-英' },
        { value: 'spell', label: '拼写' }
      ],
      selectedMode: 'en2zh',
      gradeData: null,
      firstTryCorrect: new Set(),
      wrongCount: 0,
      showAnswer: false,
      pageOptions: null,
      wrongIndexes: [],
    }
  },
  computed: {
    currentQuestion() {
      return this.questions[this.currentIndex] || {};
    }
  },
  onLoad(options) {
    try {
      // 保存页面参数
      this.pageOptions = { ...options };

      // 设置测试模式
      const { mode } = options;
      this.selectedMode = mode || 'en2zh';
      this.mode = this.selectedMode;

      // 检查是否是自定义单词模式
      if (options.isCustom === 'true' && options.words) {
        // 自定义单词模式
        const decodedWords = decodeURIComponent(options.words);
        const parsedWords = JSON.parse(decodedWords);

        // 从本地存储中获取翻译结果
        this.initCustomQuestionsFromStorage(parsedWords);
      } else {
        // 原有的单元模式
        const { grade, semester, units } = options;

        // 解码并解析 units 参数
        const decodedUnits = decodeURIComponent(units);
        const parsedUnits = JSON.parse(decodedUnits);

        // 初始化题目
        this.initQuestions(grade, semester, parsedUnits);
      }
    } catch (error) {
      console.error('Error in onLoad:', error);
      uni.showToast({
        title: '加载数据失败',
        icon: 'none'
      });
    }
  },
  methods: {
    // 添加获取年级数据的方法
    getGradeData(grade) {
      return gradeData[grade];
    },

    // 修改初始化问题的方法
    // 从本地存储中初始化自定义单词题目
    initCustomQuestionsFromStorage(words) {
      try {
        if (!words || !Array.isArray(words) || words.length === 0) {
          throw new Error('单词列表不能为空');
        }

        // 从本地存储中获取翻译结果
        const cardData = customCardStorage.getCustomCardData();
        console.log('从本地存储中获取的数据:', cardData);

        if (cardData && cardData.words && cardData.translations &&
            cardData.words.length === cardData.translations.length) {
          console.log('开始创建单词和翻译的映射');

          // 创建单词和翻译的映射
          const translationMap = {};
          for (let i = 0; i < cardData.words.length; i++) {
            const word = cardData.words[i];
            const translation = cardData.translations[i];

            // 确保单词是字符串
            const wordKey = typeof word === 'string' ? word.toLowerCase() : String(word).toLowerCase();
            translationMap[wordKey] = translation;
          }

          console.log('单词和翻译的映射创建完成:', translationMap);

          // 准备单词对象数组
          const wordObjects = [];

          // 为每个单词创建对象
          for (const word of words) {
            // 确保单词是字符串
            const wordKey = typeof word === 'string' ? word.toLowerCase() : String(word).toLowerCase();
            const translation = translationMap[wordKey] || '无翻译';

            console.log(`单词 [${word}] 的翻译: [${translation}]`);

            wordObjects.push({
              en: word,
              zh: translation
            });
          }

          // 打乱单词顺序
          const shuffledWords = this.shuffleArray([...wordObjects]);

          // 生成题目
          this.generateQuestionsFromWordObjects(shuffledWords,wordObjects);

          console.log('从本地存储中加载题目成功', {
            wordObjects,
            shuffledWords,
            questions: this.questions
          });
        } else {
          // 如果本地存储中没有数据，使用翻译 API
          console.warn('本地存储中没有有效的翻译数据，将使用翻译 API');
          this.initCustomQuestions(words);
          return;
        }
      } catch (error) {
        console.error('Error initializing custom questions from storage:', error);
        // 如果从本地存储中加载失败，尝试使用翻译 API
        this.initCustomQuestions(words);
      } finally {
        // 隐藏加载提示
        uni.hideLoading();
      }
    },

    // 从单词对象数组生成题目
    generateQuestionsFromWordObjects(wordObjects, words) {
      console.log('开始生成题目，单词对象数组:', wordObjects);

      // 生成题目
      try {
        this.questions = wordObjects.map(word => {
          if (this.selectedMode === 'spell') {
            const spellOptions = this.generateSpellingOptions(word.en);
            return {
              originalWord: word.en,
              word: spellOptions.word,
              gapStart: spellOptions.gapStart,
              gapEnd: spellOptions.gapEnd,
              options: spellOptions.options,
              correctAnswer: spellOptions.word.substring(spellOptions.gapStart, spellOptions.gapEnd),
              chinese: word.zh
            };
          } else {
            const options = this.generateOptions(word, words);
            return {
              word,
              question: this.selectedMode === 'en2zh' ? word.en : word.zh,
              options: this.selectedMode === 'en2zh' ? options.zh : options.en,
              correctAnswer: this.selectedMode === 'en2zh' ? word.zh : word.en,
              chinese: word.zh,
              en: word.en
            };
          }
        });
        console.log('Questions initialized:', this.questions);
      } catch (error) {
        console.error('生成题目数组失败:', error, {
          wordObjects,
          words
        });
        // 创建一个空的题目数组
        this.questions = [];

        // 显示错误提示
        uni.showToast({
          title: '生成题目失败，请重试',
          icon: 'none',
          duration: 2000
        });
      }
    },

    // 使用翻译 API 初始化自定义单词题目
    async initCustomQuestions(words) {
      try {
        if (!words || !Array.isArray(words) || words.length === 0) {
          throw new Error('单词列表不能为空');
        }

        // 显示加载提示
        uni.showLoading({
          title: '正在加载题目...',
          mask: true
        });

        // 将单词列表转换为卡片格式
        const wordObjects = [];

        // 如果是英文到中文模式，需要获取中文翻译
        if (this.selectedMode === 'en2zh') {
          // 将单词列表转换为逗号分隔的字符串
          const wordsStr = words.join(',');

          try {
            // 调用翻译 API
            const result = await translator.translateText(wordsStr, 'EN', 'ZH');

            // 如果翻译成功
            if (result && result.dst) {
              // 将翻译结果分割为数组
              let translations = [];

              // 先尝试使用中文逗号分割
              if (result.dst.includes('，')) {
                translations = result.dst.split('，').map(t => t.trim());
              } else {
                // 如果没有中文逗号，使用英文逗号
                translations = result.dst.split(',').map(t => t.trim());
              }

              // 确保翻译结果和单词数量一致
              if (translations.length === words.length) {
                // 将单词列表和翻译结果保存到本地存储
                const lowerWords = words.map(word => word.toLowerCase());
                customCardStorage.saveCustomCardData(lowerWords, translations);
                console.log('单词和翻译已保存到本地存储');

                // 创建卡片数据
                for (let i = 0; i < words.length; i++) {
                  wordObjects.push({
                    en: words[i],
                    zh: translations[i]
                  });
                }
              } else {
                // 如果翻译结果数量不一致，尝试单独翻译每个单词
                for (const word of words) {
                  try {
                    const singleResult = await translator.translateText(word, 'EN', 'ZH');
                    wordObjects.push({
                      en: word,
                      zh: singleResult && singleResult.dst ? singleResult.dst : '无翻译'
                    });
                  } catch (error) {
                    wordObjects.push({
                      en: word,
                      zh: '翻译失败'
                    });
                  }
                }
              }
            } else {
              // 如果翻译失败，创建没有翻译的卡片
              for (const word of words) {
                wordObjects.push({
                  en: word,
                  zh: '无翻译'
                });
              }
            }
          } catch (error) {
            console.error('翻译失败:', error);
            // 如果翻译失败，创建没有翻译的卡片
            for (const word of words) {
              wordObjects.push({
                en: word,
                zh: '翻译失败'
              });
            }
          }
        } else {
          // 如果是中文到英文模式，直接使用英文单词
          for (const word of words) {
            wordObjects.push({
              en: word,
              zh: '请输入英文' // 默认提示，实际上不会显示
            });
          }
        }

        // 打乱单词顺序
        const shuffledWords = this.shuffleArray([...wordObjects]);

        // 生成题目
        this.questions = shuffledWords.map(word => {
          if (this.selectedMode === 'spell') {
            const spellOptions = this.generateSpellingOptions(word.en);
            return {
              originalWord: word.en,
              word: spellOptions.word,
              gapStart: spellOptions.gapStart,
              gapEnd: spellOptions.gapEnd,
              chinese: word.zh,
              question: word.zh,
              answer: word.en,
              options: this.generateOptions(word.en, word.zh),
              userAnswer: '',
              isCorrect: null,
              type: 'spell'
            };
          } else if (this.selectedMode === 'en2zh') {
            return {
              question: word.en,
              answer: word.zh,
              options: this.generateOptions(word.en, word.zh),
              userAnswer: '',
              isCorrect: null,
              type: 'en2zh'
            };
          } else {
            return {
              question: word.zh,
              answer: word.en,
              options: this.generateOptions(word.en, word.zh, true),
              userAnswer: '',
              isCorrect: null,
              type: 'zh2en'
            };
          }
        });

        // 开始测试
        this.startTest();
      } catch (error) {
        console.error('Error initializing custom questions:', error);
        uni.showToast({
          title: '加载题目失败',
          icon: 'none'
        });
      } finally {
        // 隐藏加载提示
        uni.hideLoading();
      }
    },

    async initQuestions(grade, semester, units) {
      try {
        const gradeData = this.getGradeData(grade);
        if (!gradeData || !gradeData[semester]) {
          throw new Error('No data found for selected grade and semester');
        }

        const words = [];
        units.forEach(unit => {
          if (gradeData[semester][unit]) {
            words.push(...gradeData[semester][unit]);
          }
        });

        if (words.length === 0) {
          throw new Error('No words found in selected units');
        }

        const shuffledWords = this.shuffleArray([...words]);
        console.log("原始数据结构",words);
        this.questions = shuffledWords.map(word => {
          if (this.selectedMode === 'spell') {
            const spellOptions = this.generateSpellingOptions(word.en);
            return {
              originalWord: word.en,
              word: spellOptions.word,
              gapStart: spellOptions.gapStart,
              gapEnd: spellOptions.gapEnd,
              options: spellOptions.options,
              correctAnswer: spellOptions.word.substring(spellOptions.gapStart, spellOptions.gapEnd),
              chinese: word.zh
            };
          } else {
            const options = this.generateOptions(word, words);
            return {
              word,
              question: this.selectedMode === 'en2zh' ? word.en : word.zh,
              options: this.selectedMode === 'en2zh' ? options.zh : options.en,
              correctAnswer: this.selectedMode === 'en2zh' ? word.zh : word.en,
              chinese: word.zh,
              en: word.en
            };
          }
        });

        console.log('Questions initialized:', this.questions);
      } catch (error) {
        console.error('Error initializing questions:', error);
        uni.showToast({
          title: '初始化题目失败',
          icon: 'none'
        });
      }
    },

    // 重新测试
    retryTest() {
      this.currentIndex = 0;
      this.answered = false;
      this.selectedAnswer = -1;
      this.correctCount = 0;
      this.showResult = false;
      this.resultList = [];
      this.wrongCount = 0;
      this.showAnswer = false;
      this.firstTryCorrect.clear();

      // 重新打乱题目顺序
      this.questions = this.questions.map(q => ({
        ...q,
        options: this.shuffleArray([...q.options])
      }));
      this.questions = this.shuffleArray([...this.questions]);

      // 重新开始测试
      this.testing = true;
    },

    // 修改生成选项的方法
    generateOptions(currentWord, allWords, isZh2En = false) {
      console.log('生成选项，参数:', {
        currentWord,
        allWords,
        isZh2En
      });

      // 标准化 currentWord 对象
      let normalizedCurrentWord = currentWord;

      // 如果 currentWord 是字符串，则将其转换为对象
      if (typeof currentWord === 'string') {
        console.error('generateOptions: currentWord 是字符串，应该是对象', currentWord, allWords);
        return {
          en: [currentWord],
          zh: [allWords]
        };
      }

      // 处理不同的数据结构
      // 如果 currentWord 是对象，但没有 en 和 zh 属性，而是直接有 en 和 zh 属性
      if (currentWord && typeof currentWord === 'object') {
        if (currentWord.en === undefined && currentWord.zh === undefined) {
          // 尝试从对象中提取 en 和 zh 属性
          const keys = Object.keys(currentWord);
          if (keys.includes('en') && keys.includes('zh')) {
            normalizedCurrentWord = {
              en: currentWord.en,
              zh: currentWord.zh
            };
          } else {
            console.error('generateOptions: currentWord 结构不正确', currentWord);
            return {
              en: ['option1', 'option2', 'option3', 'option4'],
              zh: ['选项1', '选项2', '选项3', '选项4']
            };
          }
        }
      }

      const options = {
        en: new Set([normalizedCurrentWord.en]),
        zh: new Set([normalizedCurrentWord.zh])
      };

      console.log('标准化后的单词和选项:', {
        normalizedCurrentWord,
        options
      });

      // 确保 allWords 是数组
      if (!Array.isArray(allWords)) {
        console.error('generateOptions: allWords 不是数组', allWords);
        return {
          en: Array.from(options.en),
          zh: Array.from(options.zh)
        };
      }

      // 从所有单词中随机选择不同的单词作为混淆选项
      let otherWords = [];
      try {
        otherWords = this.shuffleArray(allWords.filter(w => w !== normalizedCurrentWord));
      } catch (error) {
        console.error('过滤单词失败:', error, {
          normalizedCurrentWord,
          allWords
        });
        // 创建一个空数组作为备选
        otherWords = [];
      }

      // 添加混淆选项，确保不重复
      try {
        for (let i = 0; i < otherWords.length && (options.en.size < 4 || options.zh.size < 4); i++) {
          try {
            const word = otherWords[i];
            if (!word || typeof word !== 'object') {
              console.warn('跳过无效的单词对象:', word);
              continue;
            }

            if (options.en.size < 4 && word.en && !options.en.has(word.en)) {
              options.en.add(word.en);
            }
            if (options.zh.size < 4 && word.zh && !options.zh.has(word.zh)) {
              options.zh.add(word.zh);
            }
          } catch (error) {
            console.error('处理单词选项失败:', error, {
              index: i,
              word: otherWords[i]
            });
          }
        }
      } catch (error) {
        console.error('添加混淆选项失败:', error, {
          otherWords,
          options
        });
      }

      // 如果选项不够4个，继续添加其他单词直到够4个
      try {
        let index = 0;
        let maxAttempts = 100; // 防止无限循环

        // 生成一些默认选项，确保至少有 4 个选项
        const defaultEnOptions = [
          normalizedCurrentWord.en,
          normalizedCurrentWord.en + 's',
          normalizedCurrentWord.en + 'ing',
          normalizedCurrentWord.en + 'ed',
          'the ' + normalizedCurrentWord.en,
          'a ' + normalizedCurrentWord.en
        ];

        const defaultZhOptions = [
          normalizedCurrentWord.zh,
          normalizedCurrentWord.zh + '的',
          normalizedCurrentWord.zh + '们',
          '一个' + normalizedCurrentWord.zh,
          '这个' + normalizedCurrentWord.zh,
          '那个' + normalizedCurrentWord.zh
        ];

        while ((options.en.size < 4 || options.zh.size < 4) && index < maxAttempts) {
          try {
            // 先尝试使用 allWords 中的单词
            if (index < allWords.length) {
              const word = allWords[index % allWords.length];
              if (word && typeof word === 'object') {
                if (options.en.size < 4 && word.en && !options.en.has(word.en)) {
                  options.en.add(word.en);
                }
                if (options.zh.size < 4 && word.zh && !options.zh.has(word.zh)) {
                  options.zh.add(word.zh);
                }
              }
            } else {
              // 如果 allWords 中的单词不够，使用默认选项
              const enIndex = index % defaultEnOptions.length;
              const zhIndex = index % defaultZhOptions.length;

              if (options.en.size < 4 && !options.en.has(defaultEnOptions[enIndex])) {
                options.en.add(defaultEnOptions[enIndex]);
              }
              if (options.zh.size < 4 && !options.zh.has(defaultZhOptions[zhIndex])) {
                options.zh.add(defaultZhOptions[zhIndex]);
              }
            }
          } catch (error) {
            console.error('处理选项失败:', error, {
              index,
              allWordsLength: allWords.length
            });
          }

          index++;
        }

        // 如果还是不够 4 个选项，强制添加默认选项
        if (options.en.size < 4) {
          for (let i = 0; i < defaultEnOptions.length && options.en.size < 4; i++) {
            options.en.add(defaultEnOptions[i]);
          }
        }

        if (options.zh.size < 4) {
          for (let i = 0; i < defaultZhOptions.length && options.zh.size < 4; i++) {
            options.zh.add(defaultZhOptions[i]);
          }
        }
      } catch (error) {
        console.error('生成选项失败:', error, {
          normalizedCurrentWord,
          allWords,
          options
        });

        // 如果出错，确保至少返回一些选项
        if (options.en.size === 0) {
          options.en = new Set(['option1', 'option2', 'option3', 'option4']);
        }
        if (options.zh.size === 0) {
          options.zh = new Set(['选项1', '选项2', '选项3', '选项4']);
        }
      }

      // 转换回数组并打乱顺序
      return {
        en: this.shuffleArray([...options.en]),
        zh: this.shuffleArray([...options.zh])
      };
    },

    // 修改生成拼写选项的方法
    generateSpellingOptions(word) {
      if (!word || typeof word !== 'string') {
        console.error('Invalid word in generateSpellingOptions:', word);
        return {
          word: '',
          gapStart: 0,
          gapEnd: 0,
          options: ['a', 'e', 'i', 'o']
        };
      }

      const en = word.toLowerCase();
      const possibleGaps = this.findPossibleGaps(en);

      if (!possibleGaps || possibleGaps.length === 0) {
        console.error('No gaps found for word:', en);
        const defaultGap = {
          start: 0,
          end: Math.min(2, en.length)
        };
        const defaultOptions = new Set([
          en.substring(0, 2),
          en.substring(0, 1) + 'a',
          en.substring(0, 1) + 'e',
          en.substring(0, 1) + 'i'
        ]);
        return {
          word: en,
          gapStart: defaultGap.start,
          gapEnd: defaultGap.end,
          options: [...defaultOptions]
        };
      }

      const gapInfo = this.selectRandomGap(possibleGaps);

      if (!gapInfo || typeof gapInfo.start === 'undefined' || typeof gapInfo.end === 'undefined') {
        console.error('Invalid gap info:', gapInfo);
        return {
          word: en,
          gapStart: 0,
          gapEnd: Math.min(2, en.length),
          options: ['a', 'e', 'i', 'o']
        };
      }

      const correctOption = en.substring(gapInfo.start, gapInfo.end);
      const optionsSet = new Set([correctOption]);

      const vowels = 'aeiou';
      const consonants = 'bcdfghjklmnpqrstvwxyz';

      // 生成混淆选项
      if (correctOption.length === 1) {
        // 单字母情况
        if (vowels.includes(correctOption)) {
          // 元音情况
          vowels.split('').forEach(v => {
            if (v !== correctOption) optionsSet.add(v);
          });
        } else {
          // 辅音情况
          const similarConsonants = consonants.split('').filter(c => c !== correctOption);
          for (let i = 0; i < 5 && optionsSet.size < 4; i++) {
            optionsSet.add(similarConsonants[Math.floor(Math.random() * similarConsonants.length)]);
          }
        }
      } else {
        // 多字母情况
        // 1. 调换字母顺序
        optionsSet.add(correctOption.split('').reverse().join(''));

        // 2. 替换元音
        const withDifferentVowel = correctOption.replace(/[aeiou]/g, () =>
          vowels[Math.floor(Math.random() * vowels.length)]);
        if (withDifferentVowel !== correctOption) {
          optionsSet.add(withDifferentVowel);
        }

        // 3. 替换辅音
        const withDifferentConsonant = correctOption.replace(/[bcdfghjklmnpqrstvwxyz]/g, () =>
          consonants[Math.floor(Math.random() * consonants.length)]);
        if (withDifferentConsonant !== correctOption) {
          optionsSet.add(withDifferentConsonant);
        }

        // 如果还不够4个选项，添加随机组合
        while (optionsSet.size < 4) {
          const randomOption = Array(correctOption.length).fill('')
            .map(() => Math.random() < 0.5 ?
              vowels[Math.floor(Math.random() * vowels.length)] :
              consonants[Math.floor(Math.random() * consonants.length)])
            .join('');
          if (randomOption !== correctOption) {
            optionsSet.add(randomOption);
          }
        }
      }

      // 确保正好有4个选项
      const finalOptions = [...optionsSet].slice(0, 4);
      while (finalOptions.length < 4) {
        const randomLetter = Math.random() < 0.5 ?
          vowels[Math.floor(Math.random() * vowels.length)] :
          consonants[Math.floor(Math.random() * consonants.length)];
        if (!finalOptions.includes(randomLetter)) {
          finalOptions.push(randomLetter);
        }
      }

      return {
        word: en,
        gapStart: gapInfo.start,
        gapEnd: gapInfo.end,
        options: this.shuffleArray(finalOptions)
      };
    },

    // 查找可能的挖空位置
    findPossibleGaps(word) {
      const gaps = [];
      const patterns = [
        /[aeiou]{1,2}/g,  // 元音
        /[bcdfghjklmnpqrstvwxyz]{2}/g,  // 连续辅音
        /[a-z]{2}e$/g,  // 以e结尾的组合
        /[a-z]h/g,  // h组合
        /[a-z]{2}y$/g,  // 以y结尾的组合
      ];

      patterns.forEach(pattern => {
        let match;
        while ((match = pattern.exec(word)) !== null) {
          gaps.push({
            start: match.index,
            end: match.index + match[0].length,
            text: match[0]
          });
        }
      });

      // 如果没有找到符合模式的，随机选择2-3个字母
      if (gaps.length === 0) {
        const length = Math.min(word.length, Math.floor(Math.random() * 2) + 2);
        const start = Math.floor(Math.random() * (word.length - length + 1));
        gaps.push({
          start,
          end: start + length,
          text: word.substring(start, start + length)
        });
      }

      return gaps;
    },

    // 随机选择一个挖空位置
    selectRandomGap(gaps) {
      return gaps[Math.floor(Math.random() * gaps.length)];
    },

    // 修改选择答案的方法
    selectAnswer(index) {
      if (!this.testing || (this.answered && this.selectedMode !== 'spell')) {
        return;
      }

      const currentQ = this.currentQuestion;

      if (this.selectedMode === 'spell') {
        const isCorrect = currentQ.options[index] === currentQ.correctAnswer;

        if (!isCorrect) {
          this.wrongCount++;
          // 播放错误提示音
          uni.vibrateShort();
          //const errorAudio = uni.createInnerAudioContext();
          // errorAudio.src = '/static/audio/error.mp3';
          // errorAudio.play();

          // 添加抖动动画
          this.wrongIndexes.push(index);
          setTimeout(() => {
            const idx = this.wrongIndexes.indexOf(index);
            if (idx > -1) {
              this.wrongIndexes.splice(idx, 1);
            }
          }, 500);
          return;
        }

        // 答对了，显示正确答案并播放单词语音
        this.showAnswer = true;
        this.answered = true;
        // 播放单词语音
        const wordAudio = uni.createInnerAudioContext();
        wordAudio.src = `https://dict.youdao.com/dictvoice?audio=${currentQ.originalWord}&type=1`;
        wordAudio.play();

        // 记录是否一次通过
        if (this.wrongCount === 0) {
          this.firstTryCorrect.add(currentQ.originalWord);
        }

        this.correctCount++;
        this.resultList.push({
          question: currentQ.question,
          correctAnswer: currentQ.correctAnswer,
          selectedAnswer: currentQ.options[index],
          isCorrect: true,
          wrongAttempts: this.wrongCount,
          word: currentQ.originalWord,
          chinese: currentQ.chinese
        });

        // 延迟进入下一题
        setTimeout(() => {
          if (this.currentIndex < this.questions.length - 1) {
            this.showAnswer = false;  // 重置显示状态
            this.currentIndex++;
            this.wrongCount = 0;
            this.answered = false;
          } else {
            this.showResult = true;
          }
        }, 2000);  // 延长等待时间到2秒，让用户能看清答案

        return;
      }

      // 其他模式的原有逻辑
      this.answered = true;
      this.selectedAnswer = index;

      const isCorrect = this.currentQuestion.options[index] === this.currentQuestion.correctAnswer;

      if (isCorrect) {
        this.correctCount++;
        // 播放单词语音
        const wordAudio = uni.createInnerAudioContext();
        wordAudio.src = `https://dict.youdao.com/dictvoice?audio=${currentQ.word.en}&type=1`;
        wordAudio.play();
      }

      this.resultList.push({
        question: this.currentQuestion.question,
        correctAnswer: this.currentQuestion.correctAnswer,
        selectedAnswer: this.currentQuestion.options[index],
        isCorrect,
        word: currentQ.word.en,
        chinese: currentQ.word.zh
      });

      setTimeout(() => {
        if (this.currentIndex < this.questions.length - 1) {
          this.currentIndex++;
          this.answered = false;
          this.selectedAnswer = -1;
        } else {
          this.showResult = true;
        }
      }, 2000);
    },

    // 获取选项样式
    getOptionClass(index) {
      if (!this.answered) return '';


      if (this.currentQuestion.options[index] === this.currentQuestion.correctAnswer) {
        return 'correct';
      }

      if (index === this.selectedAnswer && this.currentQuestion.options[index] !== this.currentQuestion.correctAnswer) {
        return 'wrong';
      }

      return '';
    },

    // 数组随机打乱
    shuffleArray(array) {
      const shuffled = [...array];  // 创建数组副本
      for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
      }
      return shuffled;  // 返回打乱后的新数组
    },

    // 修改切换模式的方法
    switchMode(mode) {
      try {
        if (!this.pageOptions) {
          throw new Error('页面参数丢失');
        }

        this.selectedMode = mode;
        this.mode = mode;
        this.currentIndex = 0;
        this.answered = false;
        this.selectedAnswer = -1;
        this.correctCount = 0;
        this.showResult = false;
        this.resultList = [];
        this.wrongCount = 0;
        this.firstTryCorrect.clear();

        // 检查是否是自定义单词模式
        if (this.pageOptions.isCustom === 'true' && this.pageOptions.words) {
          // 自定义单词模式
          const decodedWords = decodeURIComponent(this.pageOptions.words);
          const parsedWords = JSON.parse(decodedWords);

          // 从本地存储中获取翻译结果
          this.initCustomQuestionsFromStorage(parsedWords);
        } else {
          // 原有的单元模式
          const { grade, semester, units } = this.pageOptions;

          // 解码并解析 units 参数
          const decodedUnits = decodeURIComponent(units);
          const parsedUnits = JSON.parse(decodedUnits);

          // 初始化题目
          this.initQuestions(grade, semester, parsedUnits);
        }
      } catch (error) {
        console.error('Error in switchMode:', error);
        uni.showToast({
          title: '切换模式失败',
          icon: 'none'
        });
      }
    },

    // 添加返回处理方法
    handleBack() {
      if (this.testing && this.resultList.length > 0) {
        uni.showModal({
          title: '提示',
          content: '测试尚未完成，确定要返回吗？',
          success: (res) => {
            if (res.confirm) {
              this.goBack();
            }
          }
        });
      } else {
        this.goBack();
      }
    },

    // 返回上一页
    goBack() {
      uni.navigateBack({
        delta: 1 // 返回层级，1 为上一页
      });
    },

    // 结束测试
    endTest() {
      uni.showModal({
        title: '提示',
        content: '确定要结束测试吗？',
        success: (res) => {
          if (res.confirm) {
            this.showResult = true;
            this.testing = false;
          }
        }
      });
    },

    // 开始测试
    startTest() {
      console.log('开始测试，题目数量:', this.questions ? this.questions.length : 0);

      if (!this.questions || this.questions.length === 0) {
        console.error('题目为空，无法开始测试');
        uni.showToast({
          title: '题目加载失败，请重试',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      this.testing = true;
      this.currentIndex = 0;
      this.answered = false;
      this.selectedAnswer = -1;
      this.correctCount = 0;
      this.showResult = false;
      this.resultList = [];
      this.questions = this.shuffleArray(this.questions);

      console.log('测试已开始，题目:', this.questions);
    },

    // 获取未完成的题目
    getUnfinishedQuestions() {
      const answeredQuestions = new Set(this.resultList.map(result => result.question));
      return this.questions.filter(q => !answeredQuestions.has(q.question));
    },

    // 生成拼写题目显示文本
    generateSpellingQuestion(word, spellOptions) {
      const before = word.substring(0, spellOptions.gapStart);
      const after = word.substring(spellOptions.gapEnd);
      const gapLength = spellOptions.gapEnd - spellOptions.gapStart;
      const underscores = '_'.repeat(gapLength);  // 根据缺失部分的长度生成对应数量的下划线
      return `${before}${underscores}${after}`;
    },

    // 检查单词是否在错题本中
    isInErrorBook(word) {
      return errorBookManager.isInErrorBook(word);
    },

    // 添加到错题本
    addToErrorBook(word) {
      // 判断当前词语是否为英文
      const isEnglish = /^[a-zA-Z\s]+$/.test(word);

      // 添加到错题本
      const result = errorBookManager.addToErrorBook(word);

      // 显示提示
      uni.showToast({
        title: isEnglish ? '已加入英文错题本' : '已加入中文错题本',
        icon: 'none',
        duration: 2000
      });

      console.log('添加到错题本:', {
        word,
        isEnglish,
        result
      });

      // 强制更新视图
      this.$forceUpdate();
    },

    // 跳转到听写页面
    goDictation() {
      try {
        // 收集所有英文单词
        const words = [];

        // 从已完成的题目中收集英文单词
        this.resultList.forEach(result => {
          if (result.word && result.word.en) {
            words.push(result.word.en); // 正确提取英文单词
          } else if (typeof result.word === 'string') {
            words.push(result.word);
          }
        });

        // 从未完成的题目中收集英文单词
        const unfinishedQuestions = this.getUnfinishedQuestions();
        unfinishedQuestions.forEach(question => {
          if (question.word && question.word.en) {
            words.push(question.word.en); // 正确提取英文单词
          } else if (question.en) {
            words.push(question.en); // 如果 question 本身有 en 属性
          } else if (typeof question.word === 'string') {
            words.push(question.word);
          }
        });

        // 如果没有单词，显示提示
        if (words.length === 0) {
          uni.showToast({
            title: '没有可用的单词进行听写',
            icon: 'none'
          });
          return;
        }

        // 将单词列表转换为逗号分隔的字符串
        const wordsStr = words.join(',');

        // 跳转到听写页面，并传递单词列表，始终开启英文模式
        uni.navigateTo({
          url: `/pages/dictation/index?words=${encodeURIComponent(wordsStr)}&isEn=true`
        });
      } catch (error) {
        console.error('跳转到听写页面失败:', error);
        uni.showToast({
          title: '跳转失败，请重试',
          icon: 'none'
        });
      }
    }
  }
}
</script>

<style>
.container{
  min-height: calc(100vh - 100rpx);
}
/* 修改抖动动画的类名 */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

.shake-animation {
  animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
}

/* 添加拼写模式的中文提示样式 */
.spell-chinese {
  font-size: 36rpx;
  color: #393C98;
  opacity: 0.5;
}

.spell-word {
  font-size: 48rpx;
  letter-spacing: 4rpx;
  font-family: Arial, sans-serif;
  color: #333;
}

/* 拼写题目样式 */
.question.spell-mode {
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 修改选项样式 */
.option-item {
  padding: 30rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  font-size: 32rpx;
  color: #333;
  text-align: center;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.option-item:active {
  transform: scale(0.98);
}

.option-item.correct {
  background-color: #382BA7;
  color: #fff;
}

.option-item.wrong {
  background-color: #DF6B67;
  color: #fff;
}

/* 结果列表样式增强 */
.result-item {
  padding: 20rpx;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
}

.result-item.perfect {
  background-color: rgba(58, 60, 144, 0.1);
}

.result-item.perfect::after {
  content: '✨';
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 40rpx;
}

.result-item .attempts {
  font-size: 24rpx;
  color: #666;
  margin-top: 10rpx;
}

.result-item .attempts.multiple {
  color: #DF6B67;
}

/* 题目区域样式优化 */
.question-area {
  padding: 30rpx;
}

.question-card {
  background-color: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.question {
  font-size: 45rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 40rpx;
  text-align: center;
  line-height: 1.6;
}

/* 选项样式优化 */
.options {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.option-item {
  padding: 30rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  font-size: 32rpx;
  color: #333;
  text-align: center;
  transition: all 0.3s;
  border: 2rpx solid transparent;
}

.option-item:active {
  transform: scale(0.98);
  background-color: #f0f0f0;
}

/* 拼写模式的选项样式 */
.spell-mode .option-item {
  font-family: Arial, sans-serif;
  font-size: 40rpx;
  letter-spacing: 2rpx;
  padding: 20rpx 40rpx;
}

.spell-mode .option-item.correct {

  background-color:#393C98;
}

/* 添加下划线和文字动画效果 */
.gap-text {
  position: relative;
  color: transparent;

}

.gap-text::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: -4rpx;
  height: 2px;
  background-color: #333;

}

.gap-text.show-answer {
  color: #333;
  animation: popIn 0.5s ease forwards;
}

@keyframes popIn {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 正确选项的动画效果 */
.option-item.correct {
  background-color: #382BA7;
  color: #fff;
  animation: correctPop 0.5s ease forwards;
}

@keyframes correctPop {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* 添加遮罩层样式 */
.mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;

  background: linear-gradient(0deg, rgba(255,255,255, 0.7) 0%, rgba(255,255,255, 0.7) 5%,rgba(255,255,255, 0.7) 95%, rgba(255,255,255,0.7) 100%);

  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.mask-text {
  font-size: 40rpx;
  line-height: 1.5;
  color: white;
  padding: 40rpx 40rpx;
  color:#393C98;
  border-radius: 40rpx;
  background: #fff;
  text-align: center;
}

.end-test-btn {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background-color: #393C98;
  color: #fff;
  border-radius: 45rpx;
  font-size: 32rpx;
}
.result-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.result-content {
  width: 90%;
  max-height: 80vh;
  background-color: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
}

.result-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.result-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.result-score {
  font-size: 32rpx;
  color: #666;
  margin-top: 10rpx;
  display: block;
}

.result-list {
  max-height: 50vh;
}

.result-item.correct {
  background-color: white;
}

.result-item.wrong {
  background-color: rgba(255, 82, 82, 0.1);
}

.result-question {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.result-answer {
  font-size: 28rpx;
  color: #666;
}

.wrong-answer {
  color: #DF6B67;
  margin-left: 20rpx;
}

.result-actions {
  margin-top: 40rpx;
  display: flex;
  gap: 20rpx;
}

.result-actions button {
  flex: 1;
  font-size: 32rpx;
}

.dictation-btn {
  background-color: #2196F3;
  color: #fff;
}

.result-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  padding: 20rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.result-item.unfinished {
  background-color: rgba(0, 0, 0, 0.05);
}

.question-area.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.option-item[disabled] {
  pointer-events: none;
  opacity: 0.6;
}

/* 添加单词提示样式 */
.word-hint {
  margin-top: 10rpx;
  padding: 10rpx;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 8rpx;
}

/* 错题标记按钮样式 */
.error-mark {
  display: inline-block;
  width: 60rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  background-color: #f8f8f8;
  color: #666;
  border-radius: 50%;
  margin-left: 20rpx;
  font-size: 28rpx;
  border: 1px solid #ddd;
}

.error-mark.active {
  background-color: #DF6B67;
  color: #fff;
  border-color: #DF6B67;
  opacity: 0.6;
  pointer-events: none;
}

.complete-word {
  font-size: 28rpx;
  color: #393C98;
  font-weight: 500;
  margin-right: 10rpx;
}

.word-chinese {
  font-size: 24rpx;
  color: #666;
}

/* 添加结果列表的样式 */
.word-text {
  font-size: 32rpx;
  color: #333;
  margin-right: 20rpx;
}

.word-chinese {
  font-size: 28rpx;
  color: #666;
  margin-left: 20rpx;
}

.result-question {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}
</style>