<template>
	<view class="invite-container">
		<view class="invite-card">
			<view class="card-header">
				<text class="title">邀请码</text>
			</view>
			
			<view class="input-area">
				<input 
					v-model="inviteCode"
					class="invite-input"
					placeholder="请输入邀请码"
					placeholder-class="input-placeholder"
					@input="handleInput"
				/>
			</view>
			
			<view class="button-area">
				<button 
					class="submit-btn"
					:class="{ 'disabled': !inviteCode }"
					@click="verifyCode"
				>验证</button>
			</view>
		</view>
	</view>
</template>

<script>
import { userAuth } from '@/utils/UserAuth.js';

export default {
	data() {
		return {
			inviteCode: ''
		}
	},
	methods: {
		handleInput(e) {
			this.inviteCode = e.target.value.toUpperCase();
		},
		
		verifyCode() {
			if (!this.inviteCode) {
				uni.showToast({
					title: '请输入邀请码',
					icon: 'none'
				});
				return;
			}
			
			const result = userAuth.verifyInviteCode(this.inviteCode);
			
			if (result.success) {
				uni.showToast({
					title: result.message,
					icon: 'success',
					duration: 2000
				});
				
				// 延迟返回首页
				setTimeout(() => {
					uni.navigateBack();
				}, 2000);
			} else {
				uni.showToast({
					title: result.message,
					icon: 'none'
				});
			}
		}
	}
}
</script>

<style>
.invite-container {
	padding: 40rpx;
	min-height: 100vh;
	background-color: #f5f5f5;
	display: flex;
	align-items: start;
	justify-content: center;
}

.invite-card {
	width: 100%;
	background-color: #fff;
	border-radius: 20rpx;
	padding: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.card-header {
	text-align: center;
	margin-bottom: 60rpx;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
	display: block;
}

.subtitle {
	font-size: 28rpx;
	color: #666;
	display: block;
}

.input-area {
	margin-bottom: 60rpx;
}

.invite-input {
	width: auto;
	height: 88rpx;
	background-color: #f8f8f8;
	border-radius: 44rpx;
	padding: 0 40rpx;
	font-size: 32rpx;
	color: #333;
	display: block;
	text-transform: uppercase;
}

.input-placeholder {
	color: #999;
}

.button-area {
	padding: 0 40rpx;
}

.submit-btn {
	width: 100%;
	height: 88rpx;
	background-color: #393C98;
	color: #fff;
	border-radius: 44rpx;
	font-size: 32rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.submit-btn.disabled {
	opacity: 0.5;
	background-color: #999;
}
</style> 