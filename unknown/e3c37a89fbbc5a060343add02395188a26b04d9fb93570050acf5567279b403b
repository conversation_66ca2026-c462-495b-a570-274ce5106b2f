.action-btn,.share-test-btn{
  &.disabled{
  color: rgba(255, 255, 255, 0.5) !important;
  .share-icon{
    opacity: 0.5 !important;
  }
  uni-text{
    opacity: 0.5 !important;
  }
}
}
/* 通用页面容器 */
.container {
    min-height: 100vh;
    background-color: #f8f8f8;
    padding: 30rpx;
  }
  
  /* 通用头部样式 */
  .header {
    margin-bottom: 40rpx;
  }
  
  .back-btn {
    width: 160rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    padding:0 20rpx;
    border-radius:10rpx;
    box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
    margin-bottom: 20rpx;
    font-size: 36rpx;
  }
  
  .back-icon {
    font-size: 36rpx;
    color: #393C98;
    color:red;
  }
  
  .header-content {
    margin-bottom: 30rpx;
  }
  
  .title {
    font-size: 48rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 10rpx;
  }
  
  .subtitle {
    font-size: 28rpx;
    color: #666;
  }
  