/**
 * 修改 URL 中的参数
 * @param {string} url - 原始 URL
 * @param {string} key - 要修改的参数名
 * @param {string|boolean|number} value - 新的参数值
 * @returns {string} - 修改后的 URL
 */
export const updateUrlParam = (url, key, value) => {
    // 确保 value 是字符串
    const stringValue = String(value);
    
    // 解析 URL 和查询字符串
    let [baseUrl, queryString] = url.split('?');
    let params = new Map();
    
    // 如果有查询字符串，解析现有参数
    if (queryString) {
        queryString.split('&').forEach(param => {
            const [paramKey, paramValue] = param.split('=');
            if (paramKey) {
                params.set(paramKey, paramValue || '');
            }
        });
    }
    
    // 更新或添加新参数
    params.set(key, stringValue);
    
    // 重建查询字符串，不对值进行编码
    const newQueryString = Array.from(params.entries())
        .map(([k, v]) => `${k}=${v}`)
        .join('&');
    
    // 返回完整的 URL
    return newQueryString ? `${baseUrl}?${newQueryString}` : baseUrl;
};

/**
 * 批量更新 URL 参数
 * @param {string} url - 原始 URL
 * @param {Object} updates - 要更新的参数对象，key 为参数名，value 为新值
 * @returns {string} - 修改后的 URL
 */
export const updateUrlParams = (url, updates) => {
    let currentUrl = url;
    for (const [key, value] of Object.entries(updates)) {
        currentUrl = updateUrlParam(currentUrl, key, value);
    }
    return currentUrl;
}; 