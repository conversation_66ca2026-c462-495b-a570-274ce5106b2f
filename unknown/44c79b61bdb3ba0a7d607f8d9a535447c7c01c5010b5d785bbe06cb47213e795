/**
 * 翻译服务使用示例
 * 使用整合的翻译服务，提供容错机制
 */

import translator from '@/utils/translationService.js';

/**
 * 翻译单词列表示例
 * @param {Array} words - 要翻译的单词列表
 * @param {String} direction - 翻译方向，'en2zh'（英文到中文）或 'zh2en'（中文到英文）
 */
export const translateWordListExample = async (words, direction = 'en2zh') => {
  try {
    // 显示加载提示
    uni.showLoading({
      title: '翻译中...'
    });

    // 调用翻译API
    const results = await translator.translateWords(words, direction);

    // 隐藏加载提示
    uni.hideLoading();

    // 返回翻译结果
    return results;
  } catch (error) {
    // 隐藏加载提示
    uni.hideLoading();

    // 显示错误提示
    uni.showToast({
      title: `翻译失败: ${error.message || '未知错误'}`,
      icon: 'none'
    });

    // 返回空数组
    return [];
  }
};

/**
 * 翻译单个单词示例
 * @param {String} word - 要翻译的单词
 * @param {String} direction - 翻译方向，'en2zh'（英文到中文）或 'zh2en'（中文到英文）
 */
export const translateSingleWordExample = async (word, direction = 'en2zh') => {
  try {
    // 显示加载提示
    uni.showLoading({
      title: '翻译中...'
    });

    // 调用翻译API
    const result = await translator.translateWord(word, direction);

    // 隐藏加载提示
    uni.hideLoading();

    // 返回翻译结果
    return result;
  } catch (error) {
    // 隐藏加载提示
    uni.hideLoading();

    // 显示错误提示
    uni.showToast({
      title: `翻译失败: ${error.message || '未知错误'}`,
      icon: 'none'
    });

    // 返回空字符串
    return '';
  }
};

/**
 * 文本翻译示例
 * @param {String} text - 要翻译的文本
 * @param {String} source - 源语言，如 'en', 'zh'
 * @param {String} target - 目标语言，如 'en', 'zh'
 */
export const translateTextExample = async (text, source = 'en', target = 'zh') => {
  try {
    // 显示加载提示
    uni.showLoading({
      title: '翻译中...'
    });

    // 调用文本翻译API
    const result = await translator.translateText(text, source, target);

    // 隐藏加载提示
    uni.hideLoading();

    // 返回翻译结果
    return result.dst;
  } catch (error) {
    // 隐藏加载提示
    uni.hideLoading();

    // 显示错误提示
    uni.showToast({
      title: `翻译失败: ${error.message || '未知错误'}`,
      icon: 'none'
    });

    // 返回空字符串
    return '';
  }
};

/**
 * 智能翻译示例（自动检测语言并翻译）
 * @param {String} text - 要翻译的文本
 */
export const smartTranslateExample = async (text) => {
  try {
    // 显示加载提示
    uni.showLoading({
      title: '翻译中...'
    });

    // 调用智能翻译API
    const result = await translator.smartTranslate(text);

    // 隐藏加载提示
    uni.hideLoading();

    // 返回翻译结果
    return result;
  } catch (error) {
    // 隐藏加载提示
    uni.hideLoading();

    // 显示错误提示
    uni.showToast({
      title: `翻译失败: ${error.message || '未知错误'}`,
      icon: 'none'
    });

    // 返回空字符串
    return '';
  }
};

/**
 * 在页面中使用翻译工具的示例
 */
export const useTranslatorInPage = {
  data() {
    return {
      inputText: '',
      translatedText: '',
      // 翻译模式：'word' 单词翻译，'text' 文本翻译
      mode: 'word',
      // 单词翻译方向
      wordDirection: 'en2zh',
      // 文本翻译源语言
      fromLang: 'en',
      // 文本翻译目标语言
      toLang: 'zh'
    }
  },
  methods: {
    async handleTranslate() {
      if (!this.inputText.trim()) {
        uni.showToast({
          title: '请输入要翻译的文本',
          icon: 'none'
        });
        return;
      }

      try {
        // 显示加载提示
        uni.showLoading({
          title: '翻译中...'
        });

        let result;

        // 根据翻译模式选择不同的翻译方法
        if (this.mode === 'word') {
          // 单词翻译
          result = await translator.translateWord(this.inputText, this.wordDirection);
          this.translatedText = result;
        } else {
          // 文本翻译
          const textResult = await translator.translateText(
            this.inputText, 
            this.fromLang, 
            this.toLang
          );
          this.translatedText = textResult.dst;
        }

        // 隐藏加载提示
        uni.hideLoading();
      } catch (error) {
        // 隐藏加载提示
        uni.hideLoading();

        // 显示错误提示
        uni.showToast({
          title: `翻译失败: ${error.message || '未知错误'}`,
          icon: 'none'
        });
      }
    },
    
    // 切换翻译模式
    switchMode(mode) {
      this.mode = mode;
    },
    
    // 切换翻译方向
    switchDirection(direction) {
      this.wordDirection = direction;
    },
    
    // 切换源语言和目标语言
    switchLanguage() {
      const temp = this.fromLang;
      this.fromLang = this.toLang;
      this.toLang = temp;
    },
    
    // 清空输入
    clearInput() {
      this.inputText = '';
      this.translatedText = '';
    }
  }
};
