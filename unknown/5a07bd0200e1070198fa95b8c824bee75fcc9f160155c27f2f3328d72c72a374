<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="header">
      <view class="mode-selector" v-if="showModeSelector">
        <view 
          v-for="(mode, index) in modes" 
          :key="index"
          :class="['mode-item', currentMode === mode.value ? 'active' : '']"
          @click="switchMode(mode.value)"
        >
          {{ mode.label }}
        </view>
      </view>
      <view class="progress" v-if="showProgress">
        <text>{{ current }}/{{ total }}</text>
      </view>
    </view>
    
    <!-- 主要内容区域 -->
    <view class="content">
      <slot></slot>
    </view>
    
    <!-- 底部操作区域 -->
    <view class="controls" v-if="showControls">
      <slot name="controls"></slot>
    </view>
  </view>
</template>

<script>
export default {
  name: 'PageLayout',
  props: {
    showModeSelector: {
      type: Boolean,
      default: false
    },
    modes: {
      type: Array,
      default: () => []
    },
    currentMode: {
      type: String,
      default: ''
    },
    showProgress: {
      type: <PERSON><PERSON>an,
      default: false
    },
    current: {
      type: Number,
      default: 0
    },
    total: {
      type: Number,
      default: 0
    },
    showControls: {
      type: Boolean,
      default: false
    }
  },
  methods: {
 
    switchMode(mode) {
      console.log(this.showModeSelector)
      this.$emit('switch-mode', mode);
    }
  }
}
</script>

<style  scoped>
.container {
  height: calc(100vh - 100rpx);
  background-color: #f8f8f8;
  padding: 30rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  position: relative;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  width: 100%;
  box-sizing: border-box;
  z-index: 10;
  padding: 20rpx;
  gap: 20rpx;
}



.icon-back {
  font-size: 36rpx;
  color: #333;
}

.mode-selector {
  display: flex;
  gap: 10rpx;
  background-color: #fff;
  padding: 6rpx;
  border-radius: 30rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
}

.mode-item {
  padding: 8rpx 20rpx;
  font-size: 24rpx;
  color: #666;
  border-radius: 20rpx;
  transition: all 0.3s;
}

.mode-item.active {
  background-color: #393C98;
  color: #fff;
}

.progress {
  font-size: 28rpx;
  color: #666;
  background-color: #fff;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
}

.content {
 position: absolute;
  height: calc(100vh - 400rpx);
  padding: 20rpx 0;
  top:140rpx;
  left:0;
  right:0;
}

.controls {
  position: absolute;
  bottom:0;
  width: 100%;
  box-sizing: border-box;
  z-index: 1;
  padding: 30rpx;
  margin: 0 -30rpx;
  background-color: #fff;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.05);
}

/* 添加动画效果 */
.mode-item:active {
  transform: scale(0.98);
}


</style> 