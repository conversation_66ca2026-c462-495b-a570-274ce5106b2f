<template>
  <view class="translator-container">
    <view class="translator-header">
      <text class="translator-title">腾讯云翻译</text>
    </view>

    <view class="translator-mode-switch">
      <view
        class="mode-item"
        :class="{ active: mode === 'word' }"
        @tap="switchMode('word')"
      >
        <text>单词翻译</text>
      </view>
      <view
        class="mode-item"
        :class="{ active: mode === 'text' }"
        @tap="switchMode('text')"
      >
        <text>文本翻译</text>
      </view>
    </view>

    <view class="translator-options" v-if="mode === 'word'">
      <view
        class="option-item"
        :class="{ active: wordDirection === 'en2zh' }"
        @tap="switchDirection('en2zh')"
      >
        <text>英 → 中</text>
      </view>
      <view
        class="option-item"
        :class="{ active: wordDirection === 'zh2en' }"
        @tap="switchDirection('zh2en')"
      >
        <text>中 → 英</text>
      </view>
    </view>

    <view class="translator-options" v-if="mode === 'text'">
      <view class="language-selector">
        <picker
          mode="selector"
          :range="languageOptions"
          range-key="label"
          :value="getLanguageIndex(fromLang)"
          @change="onFromLangChange"
        >
          <view class="picker-view">
            <text>{{ getLanguageLabel(fromLang) }}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>

      <view class="switch-btn" @tap="switchLanguage">
        <text>⇄</text>
      </view>

      <view class="language-selector">
        <picker
          mode="selector"
          :range="languageOptions"
          range-key="label"
          :value="getLanguageIndex(toLang)"
          @change="onToLangChange"
        >
          <view class="picker-view">
            <text>{{ getLanguageLabel(toLang) }}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>
    </view>

    <view class="translator-input-area">
      <textarea
        v-model="inputText"
        class="translator-input"
        placeholder="请输入要翻译的内容"
        auto-height
      ></textarea>
      <view class="input-actions">
        <view class="clear-btn" @tap="clearInput">
          <text>清空</text>
        </view>
      </view>
    </view>

    <view class="translator-btn" @tap="handleTranslate">
      <text>翻译</text>
    </view>

    <view class="translator-result" v-if="translatedText">
      <view class="result-title">
        <text>翻译结果</text>
      </view>
      <view class="result-content">
        <text>{{ translatedText }}</text>
      </view>
      <view class="result-actions">
        <view class="copy-btn" @tap="copyResult">
          <text>复制结果</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import translator from '@/utils/translationService.js';

export default {
  name: 'TXTranslator',
  data() {
    return {
      inputText: '',
      translatedText: '',
      // 翻译模式：'word' 单词翻译，'text' 文本翻译
      mode: 'word',
      // 单词翻译方向
      wordDirection: 'en2zh',
      // 文本翻译源语言
      fromLang: 'en',
      // 文本翻译目标语言
      toLang: 'zh',
      // 语言选项
      languageOptions: [
        { value: 'zh', label: '中文' },
        { value: 'en', label: '英文' },
        { value: 'ja', label: '日文' },
        { value: 'ko', label: '韩文' },
        { value: 'fr', label: '法文' },
        { value: 'es', label: '西班牙文' },
        { value: 'it', label: '意大利文' },
        { value: 'de', label: '德文' },
        { value: 'tr', label: '土耳其文' },
        { value: 'ru', label: '俄文' },
        { value: 'pt', label: '葡萄牙文' },
        { value: 'vi', label: '越南文' },
        { value: 'id', label: '印尼文' },
        { value: 'th', label: '泰文' },
        { value: 'ms', label: '马来文' }
      ]
    }
  },
  methods: {
    async handleTranslate() {
      if (!this.inputText.trim()) {
        uni.showToast({
          title: '请输入要翻译的内容',
          icon: 'none'
        });
        return;
      }

      try {
        // 显示加载提示
        uni.showLoading({
          title: '翻译中...'
        });

        let result;

        // 根据翻译模式选择不同的翻译方法
        if (this.mode === 'word') {
          // 单词翻译
          result = await translator.translateWord(this.inputText, this.wordDirection);
          this.translatedText = result;
        } else {
          // 文本翻译
          const textResult = await translator.translateText(
            this.inputText,
            this.fromLang,
            this.toLang
          );
          this.translatedText = textResult.dst;
        }

        // 隐藏加载提示
        uni.hideLoading();
      } catch (error) {
        // 隐藏加载提示
        uni.hideLoading();

        // 显示错误提示
        uni.showToast({
          title: `翻译失败: ${error.message || '未知错误'}`,
          icon: 'none'
        });
      }
    },

    // 切换翻译模式
    switchMode(mode) {
      this.mode = mode;
    },

    // 切换翻译方向
    switchDirection(direction) {
      this.wordDirection = direction;
    },

    // 切换源语言和目标语言
    switchLanguage() {
      const temp = this.fromLang;
      this.fromLang = this.toLang;
      this.toLang = temp;
    },

    // 清空输入
    clearInput() {
      this.inputText = '';
      this.translatedText = '';
    },

    // 复制翻译结果
    copyResult() {
      if (!this.translatedText) return;

      uni.setClipboardData({
        data: this.translatedText,
        success: () => {
          uni.showToast({
            title: '已复制到剪贴板',
            icon: 'success'
          });
        }
      });
    },

    // 获取语言选项的索引
    getLanguageIndex(langCode) {
      const index = this.languageOptions.findIndex(item => item.value === langCode);
      return index >= 0 ? index : 0;
    },

    // 获取语言选项的标签
    getLanguageLabel(langCode) {
      const option = this.languageOptions.find(item => item.value === langCode);
      return option ? option.label : '未知语言';
    },

    // 源语言变更
    onFromLangChange(e) {
      const index = e.detail.value;
      this.fromLang = this.languageOptions[index].value;
    },

    // 目标语言变更
    onToLangChange(e) {
      const index = e.detail.value;
      this.toLang = this.languageOptions[index].value;
    }
  }
}
</script>

<style scoped lang="scss">
.translator-container {
  padding: 30rpx;
  background-color: #f8f8f8;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.translator-header {
  margin-bottom: 30rpx;
}

.translator-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.translator-mode-switch {
  display: flex;
  margin-bottom: 20rpx;
  background-color: #eee;
  border-radius: 10rpx;
  overflow: hidden;
}

.mode-item {
  flex: 1;
  padding: 20rpx 0;
  text-align: center;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s;

  &.active {
    background-color: #393C98;
    color: #fff;
  }
}

.translator-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.option-item {
  flex: 1;
  padding: 16rpx 0;
  text-align: center;
  font-size: 28rpx;
  color: #666;
  background-color: #eee;
  border-radius: 8rpx;
  margin: 0 10rpx;
  transition: all 0.3s;

  &.active {
    background-color: #393C98;
    color: #fff;
  }
}

.language-selector {
  flex: 1;
}

.picker-view {
  padding: 16rpx;
  background-color: #eee;
  border-radius: 8rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 28rpx;
  color: #333;
}

.picker-arrow {
  font-size: 24rpx;
  color: #999;
}

.switch-btn {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 36rpx;
  color: #393C98;
}

.translator-input-area {
  margin-bottom: 30rpx;
}

.translator-input {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 10rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  border: 1px solid #eee;
}

.input-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 10rpx;
}

.clear-btn {
  padding: 10rpx 20rpx;
  font-size: 24rpx;
  color: #999;
}

.translator-btn {
  width: 100%;
  height: 90rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #393C98;
  color: #fff;
  font-size: 32rpx;
  border-radius: 10rpx;
  margin-bottom: 30rpx;
}

.translator-result {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 20rpx;
  border: 1px solid #eee;
}

.result-title {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.result-content {
  font-size: 30rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 20rpx;
}

.result-actions {
  display: flex;
  justify-content: flex-end;
}

.copy-btn {
  padding: 10rpx 20rpx;
  background-color: #f0f0f0;
  border-radius: 6rpx;
  font-size: 24rpx;
  color: #666;
}
</style>
