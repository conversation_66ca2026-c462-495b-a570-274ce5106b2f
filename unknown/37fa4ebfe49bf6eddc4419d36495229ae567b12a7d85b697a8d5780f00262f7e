/**
 * DOM节点转图片工具类
 */
class Html2CanvasUtil {
  constructor() {
    // 默认配置
    this.defaultOptions = {
      backgroundColor: '#ffffff', // 背景色
      scale: 2, // 清晰度
      useCORS: true, // 允许跨域
      allowTaint: true, // 允许跨域图片
      logging: false, // 关闭日志
    }
  }

  /**
   * 将 DOM 节点转换为图片
   * @param {string} selector - 需要转换的 DOM 节点选择器
   * @param {Object} options - 配置项
   * @returns {Promise} - 返回生成的图片路径
   */
  async domToImage(selector, options = {}) {
    // #ifdef MP-WEIXIN
    return new Promise((resolve, reject) => {
      try {
        const query = uni.createSelectorQuery();
        query.select(selector)
          .fields({
            node: true,
            size: true,
            rect: true,
            scrollOffset: true,
            computedStyle: ['*']
          })
          .exec(async (res) => {
            if (!res[0]) {
              reject(new Error('未找到节点'));
              return;
            }

            const { width, height } = res[0];
            
            // 创建画布，添加额外的高度用于标题和时间
            const canvas = await new Promise((resolve, reject) => {
              uni.createSelectorQuery()
                .select('#saveCanvas')
                .fields({ node: true, size: true })
                .exec((res) => {
                  if (res[0] && res[0].node) {
                    resolve(res[0].node);
                  } else {
                    reject(new Error('创建画布失败'));
                  }
                });
            });
            
            // 设置画布尺寸，添加边距和标题空间
            const padding = 40;
            const headerHeight = 160; // 标题区域高度
            canvas.width = (width + padding * 2) * 2;
            canvas.height = (height + padding * 2 + headerHeight) * 2;
            const ctx = canvas.getContext('2d');
            
            // 设置清晰度
            ctx.scale(2, 2);
            
            // 绘制卡片背景和阴影
            ctx.save();
            ctx.shadowColor = 'rgba(0, 0, 0, 0.1)';
            ctx.shadowBlur = 20;
            ctx.shadowOffsetX = 0;
            ctx.shadowOffsetY = 4;
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(padding, padding, width + padding * 2, height + headerHeight + padding * 2);
            ctx.restore();

            try {
              // 绘制小程序 logo
              const logoPath = '/static/logo.png'; // 请确保路径正确
              await this._drawImage(ctx, logoPath, padding + 20, padding + 20, 40, 40);
              
              // 绘制标题
              ctx.fillStyle = '#333333';
              ctx.font = 'bold 36px sans-serif';
              ctx.textBaseline = 'middle';
              ctx.fillText('听写结果', padding + 80, padding + 40);
              
              // 绘制时间
              const now = new Date();
              const timeStr = `时间：${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;
              ctx.font = '28px sans-serif';
              ctx.fillStyle = '#666666';
              ctx.fillText(timeStr, padding + 20, padding + 100);
              
              // 获取节点内容
              const nodeList = await this._getNodeList(selector);
              
              // 绘制节点内容，向下偏移 headerHeight
              await this._drawNodes(ctx, nodeList, {
                offsetX: nodeList[0].left - padding,
                offsetY: nodeList[0].top - (padding + headerHeight),
                width,
                height
              });

              // 将画布内容转换为图片
              uni.canvasToTempFilePath({
                canvas,
                width: canvas.width,
                height: canvas.height,
                destWidth: canvas.width,
                destHeight: canvas.height,
                fileType: 'png',
                quality: 1,
                success: (res) => {
                  // 转发到微信聊天
                  resolve(res.tempFilePath);
                },
                fail: (err) => {
                  reject(err);
                }
              });
            } catch (err) {
              reject(err);
            }
          });
      } catch (error) {
        reject(error);
      }
    });
    // #endif

    // #ifndef MP-WEIXIN
    return Promise.reject(new Error('此功能仅支持微信小程序'));
    // #endif
  }

  /**
   * 绘制图片
   * @private
   */
  async _drawImage(ctx, path, x, y, width, height) {
    return new Promise((resolve, reject) => {
      // #ifdef MP-WEIXIN
      const image = wx.createImage();
      image.onload = () => {
        try {
          ctx.drawImage(image, x, y, width, height);
          resolve();
        } catch (err) {
          console.error('绘制图片失败:', err);
          reject(err);
        }
      };
      image.onerror = (err) => {
        console.error('图片加载失败:', err);
        reject(err);
      };
      image.src = path;
      // #endif
    });
  }

  /**
   * 获取节点列表信息
   * @private
   */
  async _getNodeList(selector) {
    return new Promise((resolve, reject) => {
      const query = uni.createSelectorQuery();
      query.selectAll(`${selector}, ${selector} .result-item, ${selector} .result-word, ${selector} .pause-time, ${selector} .result-status`)
        .fields({
          dataset: true,
          size: true,
          rect: true,
          computedStyle: ['fontSize', 'color', 'backgroundColor', 'padding', 'margin', 'textAlign']
        })
        .exec(res => {
          if (res[0]) {
            resolve(res[0]);
          } else {
            reject(new Error('获取节点信息失败'));
          }
        });
    });
  }

  /**
   * 绘制节点内容
   * @private
   */
  async _drawNodes(ctx, nodes, layout) {
    for (const node of nodes) {
      // 计算相对位置
      const x = node.left - layout.offsetX;
      const y = node.top - layout.offsetY;
      
      // 绘制背景
      if (node.backgroundColor && node.backgroundColor !== 'rgba(0, 0, 0, 0)') {
        ctx.fillStyle = node.backgroundColor;
        ctx.fillRect(x, y, node.width, node.height);
      }
      
      // 绘制文本
      if (node.dataset && node.dataset.text) {
        ctx.fillStyle = node.color || '#000000';
        ctx.font = `${node.fontSize} sans-serif`;
        ctx.textBaseline = 'middle';
        ctx.textAlign = node.textAlign || 'left';
        
        let textX = x;
        if (node.textAlign === 'center') {
          textX = x + node.width / 2;
        } else if (node.textAlign === 'right') {
          textX = x + node.width;
        }
        
        ctx.fillText(node.dataset.text, textX, y + node.height / 2);
      }
    }
  }
}

// 导出工具类实例
export const html2canvasUtil = new Html2CanvasUtil(); 