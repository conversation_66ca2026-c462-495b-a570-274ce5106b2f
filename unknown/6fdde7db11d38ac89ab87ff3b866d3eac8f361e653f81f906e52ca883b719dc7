<template>
	<view class="dictation-container">
		<!-- 添加隐藏的 canvas -->
		<canvas
			type="2d"
			id="saveCanvas"
			canvas-id="saveCanvas"
			class="hidden-canvas"
		></canvas>

		<!-- 添加隐藏的 canvas 元素 -->
		<canvas canvas-id="shareCanvas" style="width: 400px; height: 320px; position: fixed; left: -9999px;"></canvas>

		<view class="top-bar-title">
				<text>注意：您每天可使用一次听写功能，最多30个词语。完成听写或中途退出，当日次数即视为已消耗。</text>
			</view>
		<!-- 听写词组输入区域 -->
		<view class="input-area" v-if="!isDictating && !showResult">

			<view class="input-title">
				<text>请输入需要听写的词组</text>
				<text class="tip">（多个词组请用逗号或换行分隔）</text>
			</view>
			<view class="textarea-box">
				<textarea
					v-model="inputWords"
					placeholder="例如：苹果,香蕉,橙子"
					maxlength="-1"
					auto-height
					@input="handleInputChange(inputWords)"
				/>
			</view>
			<view class="toolbox">
				<view class="p_left">
					<!-- 随机听写选项 -->
					<view class="option-item">
						<switch :checked="isRandom" @change="toggleRandom" color="#393C98" />
						<text>随机顺序听写</text>
					</view>

					<!-- 新增：英语听写模式开关 -->
					<view class="option-item">
						<switch :checked="isEnglishMode" @change="toggleEnglishMode" color="#393C98" />
						<text>英语听写模式</text>
					</view>
				</view>
				<view class="p_right">
					<!-- 分享听写内容 点击按钮-->
					<button
						class="share-test-btn"
						:data-words="inputWords"
						open-type="share"
						:class="{'disabled': !inputWords.trim()}"
						:disabled="!inputWords.trim()"
					>
						<view class="btn-content">
							<image class="share-icon" src="/static/shareicon.svg" mode="aspectFit"></image>
							<text>分享听写词组</text>
						</view>
					</button>
				</view>

			</view>
			<!-- 错题本 -->
			<view class="error-word-list" v-if="currentErrorWords.length > 0">
					<view class="error-book-info">
						<view class="error-book-switch">
							<switch :checked="errorBookEnabled" @change="toggleErrorBookEnabled" color="#393C98" />
							<text>错题混入考试</text>
						</view>
						<text class="error-book-title">{{ isEnglishMode ? '英文错题本' : '中文错题本' }}({{ currentErrorWords.length }})</text>

					</view>
				</view>

		</view>

		<!-- 听写进行中的显示区域 -->
		<view class="dictation-area" v-if="isDictating">
			<view class="progress-info">
				<text class="progress-text">正在听写第 {{ completedCount}}/{{ wordsList.length }} 个词语</text>
				<view class="progress-bar">
					<view class="progress-inner" :style="{ width: progressPercent + '%' }"></view>
				</view>
			</view>

			<!-- 修改后的听写弹层 - 只显示拼音 -->
			<view class="dictation-card" >
				<view class="pinyin-display">
					<text v-if="!isEnglishMode">{{ currentPinyin || '加载中...' }}</text>
					<text v-else class="translation-text">{{ currentTranslation || '加载中...' }}</text>
				</view>

				<view class="countdown">
					<text>{{ isReading ? '正在读词...' : `下一个词语倒计时：${countdown}秒` }}</text>
				</view>
			</view>

			<!-- <view class="dictation-options">
				<switch :checked="showCurrentWord" @change="toggleShowWord" color="#393C98" />
				<text>显示当前词语</text>
			</view> -->
		</view>

		<!-- 修改读词间隔选项的显示条件，在编辑和暂停状态下显示 -->
		<view class="interval-settings" v-if="!isDictating || (isDictating && isPaused)" v-show="!showResult">
			<view class="option-title">
				<text>读词间隔时间</text>
			</view>
			<view class="time-options">
				<!-- 根据字数选项 -->
				<view
					class="time-option"
					:class="{ 'active': isCustomInterval }"
					@click="toggleCustomInterval(true)"
				>
					<text>根据字数</text>
				</view>

				<!-- 固定时间选项 -->
				<view
					v-for="option in timeOptions"
					:key="option.value"
					class="time-option"
					:class="{ 'active': !isCustomInterval && intervalTime === option.value }"
					@click="selectInterval(option.value)"
				>
					<text>{{ option.label }}</text>
				</view>
			</view>

			<!-- 每字秒数控制器 -->
			<view class="per-char-control" v-if="isCustomInterval">
				<text class="per-char-label">每字秒数:</text>
				<view class="per-char-adjuster">
					<view class="adjuster-btn" @click="adjustSecondsPerChar(-1)">
						<text class="adjuster-icon">-</text>
					</view>
					<text class="seconds-display">{{ secondsPerChar }}秒</text>
					<view class="adjuster-btn" @click="adjustSecondsPerChar(1)">
						<text class="adjuster-icon">+</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 听写结果显示 -->
		<view class="result-area" v-if="showResult">
			<view class="result-title">
				<text>听写列表</text>
			</view>

			<view class="result-list">
				<!-- 常规听写列表 -->
				<view
					v-for="(item, index) in resultList"
					:key="index"
					class="result-item"
					:class="{ 'not-dictated': !item.dictated }"
				>
					<checkbox
						:checked="selectedWords.includes(item.word)"
						@tap="toggleSelect(item.word)"
						color="#393C98"
					/>
					<text class="result-index">{{ index + 1 }}</text>
					<view class="result-word-container">
						<text class="result-word">{{ item.word }}</text>
						<text v-if="item.pauseTime > 0" class="pause-time">
							暂停时间：{{ item.pauseTime }}秒
						</text>
					</view>
					<view class="word-actions">
						<text class="result-status" v-if="!item.dictated">未听写</text>
						<!-- 如果已在错题本中，显示不可点击的蓝紫色半透明标记 -->
						<text
							class="error-mark error-mark-disabled"
							v-if="item.dictated && isInErrorBook(item.word)"
						>错</text>
						<!-- 如果在待添加列表中，显示高亮的标记 -->
						<text
							class="error-mark active"
							@tap="toggleErrorMark(item.word)"
							v-if="item.dictated && !isInErrorBook(item.word) && isPendingErrorWord(item.word)"
						>错</text>
						<!-- 如果不在错题本中且不在待添加列表中，显示普通标记 -->
						<text
							class="error-mark"
							@tap="toggleErrorMark(item.word)"
							v-if="item.dictated && !isInErrorBook(item.word) && !isPendingErrorWord(item.word)"
						>错</text>
					</view>
				</view>

				<!-- 错题本听写列表 -->
				<view class="error-book-section" v-if="resultList.some(item => (item.isErrorWord || item.isInErr) && item.dictated)">
					<view class="error-book-title">错题本词语</view>
					<view
						v-for="(item, index) in resultList.filter(item => (item.isErrorWord || item.isInErr) && item.dictated)"
						:key="'error-'+index"
						class="result-item error-item"
						:class="{ 'not-dictated': !item.dictated }"
					>

						<text class="result-index">{{ index + 1 }}</text>
						<view class="result-word-container">
							<text class="result-word">{{ item.word }}</text>
							<text v-if="item.pauseTime > 0" class="pause-time">
								暂停时间：{{ item.pauseTime }}秒
							</text>
						</view>
						<view class="word-actions">
							<text class="result-status" v-if="!item.dictated">未听写</text>
							<text
								class="correct-btn"
								:class="{ 'inactive': isMarkedCorrectInCurrentDictation(item.word) }"
								@tap="toggleCorrect(item.word)"
								v-if="item.dictated"
							>正确</text>
							<!-- 错题列表中不显示错字标记 -->
							<view class="correct-marks">
								<text
									v-for="(mark, markIndex) in 3"
									:key="markIndex"
									class="correct-mark"
									:class="{ 'active': getCorrectCount(item.word) > markIndex }"
								>✓</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 当前暂停时间显示 -->
			<view class="pause-info" v-if="isPaused">
				<text class="pause-timer">已暂停: {{ currentPauseTime }}秒</text>
			</view>

			<view class="result-summary">
				<view class="select-all-container">
					<checkbox
						:checked="selectedWords.length === wordsList.length"
						@tap="toggleSelectAll"
						color="#393C98"
					/>
					<text>全选</text>
				</view>
				<text>共 {{ wordsList.length }} 个词语，已听写 {{ completedCount }} 个</text>
			</view>
		</view>

		<!-- 操作按钮区域 -->
		<view class="action-buttons">
			<button
				v-if="!isDictating && !showResult"
				type="primary"
				class="action-btn"
				@click="handleDictation"
				:class="{'disabled': !canStartDictation}"
			>{{ getStartButtonText }}</button>

			<view class="button-group" v-if="isDictating">
				<button
					type="default"
					class="action-btn pause-btn"
					@click="togglePause"
				>{{ isPaused ? '继续' : '暂停' }}</button>

				<button
					type="warn"
					class="action-btn stop-btn"
					@click="stopDictation"
				>结束听写</button>
			</view>

			<view class="button-group" v-if="showResult">
				<button
					v-if="inputWords.trim()"
					type="primary"
					class="action-btn"
					@click="resetDictation"
				>重新听写</button>
			</view>
		</view>


	</view>
</template>

<script>
// 使用适配器导入拼音库
import pinyinPro from '@/utils/pinyin/pinyinAdapter.js';
import { audioSpeech } from '@/utils/audioSpeech.js'; // 导入新的语音工具
import { gradeData } from '@/static/data/index.js';
// 导入工具类
import { html2canvasUtil } from '@/utils/html2canvas.js';
import { dictationValidator } from '@/utils/validator.js'; // 添加验证工具导入
import { userAuth } from '@/utils/UserAuth.js';
// 导入分享图片生成工具
import { generateShareImage } from '@/utils/shareImage.js';
import { updateUrlParam } from '@/utils/urlHelper.js';
// 导入错题本管理工具
import errorBookManager from '@/utils/ErrorBookManager.js';
// 导入翻译工具
import translator from '@/utils/translationService.js';
// 导入自定义卡片存储工具
import customCardStorage from '@/utils/CustomCardStorage.js';

export default {
	data() {
		return {
			inputWords: '',
			isDictating: false,
			isPaused: false,
			wordsList: [],
			originalWordsList: [], // 保存原始顺序
			currentIndex: 0,
			countdown: 0,
			timer: null,
			showCurrentWord: false,
			showResult: false, // 是否显示结果
			resultList: [], // 听写结果列表
			completedCount: 0, // 已完成的词语数量
			currentWord: '', // 当前听写词语
			currentPinyin: '', // 当前词语拼音
			currentTranslation: '', // 添加当前词语翻译
			translationCache: {}, // 翻译结果缓存

			// 新增数据属性
			isEnglishMode: false, // 英语听写模式
			intervalTime: 5, // 默认间隔时间5秒
			timeOptions: [
				{ label: '5秒', value: 5 },
				{ label: '10秒', value: 10 },
				{ label: '15秒', value: 15 }
			],
			isCustomInterval: true, // 默认使用根据字数计算的间隔时间
			secondsPerChar: 3, // 每个字符的秒数，默认为3秒
			audioCache: new Map(), // 内存缓存
			isLoadingAudio: false, // 音频加载状态
			audioType: 0, // 有道词典 0: 慢速, 1: 正常速度
			speechRate: 0.4, // 其他平台的语速 (0.1-1.0)
			hasInteracted: false, // 新增交互状态
			isRandom: true, // 添加默认随机听写
			startSound: null, // 开始提示音
			endSound: null, // 结束提示音
			isReading: false, // 添加朗读状态标记
			dictationOrder: [], // 添加数组来记录听写顺序
			pauseStartTime: null, // 添加暂停开始时间
			currentPauseTime: 0, // 当前暂停时间（秒）
			totalPauseTime: 0, // 总暂停时间（秒）
			pauseTimer: null, // 暂停计时器
			isPageActive: true, // 添加页面活跃状态标记
			isGeneratingImage: false, // 图片生成状态
			isWechat: false, // 是否是微信小程序环境
			share: {
				title: '七宝学习助手-语音听写',
				path: '/pages/dictation/index',
				imageUrl: '/static/share-image.png',
				desc: '快来和我一起练习听写吧',
				content: '七宝学习助手-听写助手'
			},
			remainingTime: 0, // 添加剩余时间记录
			selectedWords: [], // 添加选中词语数组
			errorBookEnabled: false, // 错题混入开关
			errorWords: {
				chinese: [], // 中文错题本
				english: [] // 英文错题本
			},
			currentErrorWords: [], // 当前模式的错题列表
			isErrorBookSection: false, // 是否正在听写错题部分
			pendingErrorWords: [], // 待添加到错题本的词语
		}
	},
	computed: {
		progressPercent() {
			if (this.wordsList.length === 0) return 0;
			return (this.completedCount / this.wordsList.length) * 100;
		},
		hasErrorWords() {
			return this.currentErrorWords.length > 0;
		},
		canStartDictation() {
			// 如果有输入词语，或者启用了错题混入且有错题，则可以开始听写
			return this.inputWords.trim() || (this.errorBookEnabled && this.currentErrorWords.length > 0);
		},
		getStartButtonText() {
			if (this.inputWords.trim()) {
				return '开始听写';
			} else if (this.errorBookEnabled && this.currentErrorWords.length > 0) {
				return `开始错题听写(${this.currentErrorWords.length}个)`;
			} else {
				return '开始听写';
			}
		}
	},
	async onLoad(options) {
		// 初始化时检查听写权限
		const result = await userAuth.checkFeaturePermission('dictation');
		if(!result.allowed){
			uni.showModal({
				title: '提示',
				content: '每人每天只有一次听写次数，请明天再来',
				showCancel: false,
				success: () => {
					uni.reLaunch({
						url: '/pages/index/index'
					});
				}
			});
			return;
		}

		// 检查是否有英语模式参数
		if (options.isEn) {
			this.isEnglishMode = options.isEn === 'true';
			// 根据模式调整每字秒数
			this.secondsPerChar = this.isEnglishMode ? 1 : 3;
		}

		// 检查是否有分享参数
		if (options.words) {
			try {
				// 使用 decodeURIComponent 解码 URL 参数
				const decodedWords = decodeURIComponent(options.words);
				// 处理可能的编码问题
				const processedWords = decodedWords
					.replace(/%20/g, ' ') // 将 %20 替换为空格
					.replace(/%2C/g, ',') // 将 %2C 替换为逗号
					.replace(/%EF%BC%8C/g, ',') // 将中文逗号的编码替换为英文逗号
					.replace(/\s+/g, ' ') // 将多个空格替换为单个空格
					.trim();

				console.log('处理后的内容:', processedWords);

				// 验证处理后的内容
				if (!processedWords) {
					throw new Error('分享内容为空');
				}

				this.inputWords = processedWords;

				// 如果是英文模式，预先加载翻译
				if (this.isEnglishMode) {
					// 从本地存储中获取翻译结果
					this.preloadTranslationsFromStorage();
				}

				// 如果启用了随机顺序，打乱单词顺序
				if (this.isRandom) {
					this.inputWords = this.shuffleWords(this.inputWords);
				}
				// 更新分享链接
				this.updateSharePath();

				console.log('成功解析分享内容:', {
					original: options.words,
					decoded: decodedWords,
					processed: processedWords
				});
			} catch (error) {
				console.error('解析分享内容失败:', error);
				uni.showToast({
					title: '解析分享内容失败',
					icon: 'none',
					duration: 2000
				});
			}
		}

		//校验是不是 vip 提示没人每天只有一次听写提示弹窗
		//const isVip = userAuth.isVipUser();
		// if(!isVip){
		// 	//两秒消失
		// 	uni.showToast({
		// 				title: '您每天只有1次听写次数！',
		// 				icon: 'none',
		// 				duration: 4000,
		// 			});

		// }

		try {
			// 检查是否从课内听写进入
			if (options.grade && options.semester && options.units) {
				// 从课内听写进入，自动设置英语模式
				this.isEnglishMode = true;
				// 设置英语听写的时间间隔
				this.secondsPerChar = 1; // 英文模式每字1秒

				// 获取选中单元的单词
				const decodedUnits = decodeURIComponent(options.units);
				const units = JSON.parse(decodedUnits);
				let words = [];

				// 从 gradeData 中获取选中单元的单词和短语
				if (gradeData[options.grade] && gradeData[options.grade][options.semester]) {
					units.forEach(unit => {
						if (gradeData[options.grade][options.semester][unit]) {
							const unitWords = gradeData[options.grade][options.semester][unit];
							words = [...words, ...unitWords.map(word => word.en)];
						}
					});

					// 设置输入框内容，使用逗号分隔
					this.inputWords = words.join(',');

					// 如果启用了随机顺序，打乱单词顺序
					if (this.isRandom) {
						this.inputWords = this.shuffleWords(this.inputWords);
					}
				}
			}
		} catch (error) {
			console.error('Error parsing options:', error);
			uni.showToast({
				title: '加载数据失败',
				icon: 'none'
			});
		}

		// #ifdef H5
		// 检查浏览器是否支持语音合成API
		if (typeof window !== 'undefined' && window.speechSynthesis) {
			this.speechSynthesis = window.speechSynthesis;
			// 初始化语音实例
			this.speechUtterance = new SpeechSynthesisUtterance();
			this.speechUtterance.rate = this.speechRate;
			this.speechUtterance.pitch = 1;
			this.speechUtterance.volume = 1;
		} else {
			uni.showToast({
				title: '当前浏览器不支持语音功能',
				icon: 'none',
				duration: 2000
			});
		}
		// #endif

		// 从本地存储加载缓存的音频URL
		try {
			const cachedUrls = uni.getStorageSync('audioUrlCache');
			if (cachedUrls) {
				this.audioCache = new Map(JSON.parse(cachedUrls));
			}
		} catch (e) {
			console.error('加载音频缓存失败:', e);
		}

		// 判断是否是微信小程序环境
		// #ifdef MP-WEIXIN
		this.isWechat = true;
		// #endif

		// #ifdef MP-WEIXIN
		// 配置分享菜单
		uni.showShareMenu({
			withShareTicket: true,
			menus: ['shareAppMessage', 'shareTimeline'],
			success: function() {
				console.log('showShareMenu success');
			},
			fail: function(err) {
				console.log('showShareMenu fail', err);
			}
		});
		// #endif

		this.initErrorBook();
	},
	onShow() {


		// 页面显示时重置状态
		this.isPageActive = true;
		this.isPlaying = false;
		this.isPaused = false;
		this.currentPauseTime = 0;
		this.pauseStartTime = 0;
		this.totalPauseTime = 0;
		this.cleanupResources();
	},
	onHide() {
		// 页面隐藏时停止听写
		this.isPageActive = false;
		this.stopDictation();
		// 保存待添加的错题
		this.savePendingErrorWords();
	},
	onUnload() {
		// 页面卸载时清理资源
		this.isPageActive = false;
		this.cleanupResources();
		this.clearAudioCache();
		// 保存待添加的错题
		this.savePendingErrorWords();
		// #ifdef H5
		if (this.speechSynthesis) {
			this.speechSynthesis.cancel();
		}
		// #endif
	},
	methods: {
		// 修改朗读方法
		speakWord(word) {
			return audioSpeech.speak({
				text: word
			}).catch(error => {
				console.error('语音播放失败:', error);
				uni.showToast({
					title: '语音播放失败',
					icon: 'none'
				});
			});
		},
		handleDictation(){
			// 检查用户输入
			// 再次检查听写权限接受
			var canDictate = userAuth.checkFeaturePermission('dictation');
			canDictate.then(res=>{
				console.log('听写状态', res);
				if(res.allowed){
					this.startDictation();
				}else{
					uni.showModal({
					title: '提示',
					content: '每人每天只有一次听写次数，请明天再来',
					showCancel: false,
					success: () => {
						uni.reLaunch({
							url: '/pages/index/index'
						});
					}
				});
				return;
			  }
			})

		},
		// 预先翻译所有单词
		async preTranslateAllWords(words) {
			if (!this.isEnglishMode || !words || words.length === 0) return;

			// 显示加载提示
			uni.showLoading({
				title: '正在加载翻译...',
				mask: true
			});

			try {
				// 首先检查哪些单词需要翻译
				const wordsToTranslate = [];
				const lowerWords = words.map(word => word.toLowerCase());

				// 先在 gradeData 中查找翻译
				for (const word of lowerWords) {
					// 如果已经有缓存，跳过
					if (this.translationCache[word]) continue;

					// 在 gradeData 中查找
					let found = false;
					for (const grade in gradeData) {
						for (const semester in gradeData[grade]) {
							for (const unit in gradeData[grade][semester]) {
								const unitWords = gradeData[grade][semester][unit];
								const wordData = unitWords.find(w => w.en.toLowerCase() === word);
								if (wordData) {
									// 如果找到了，缓存这个翻译
									this.translationCache[word] = wordData.zh;
									found = true;
									break;
								}
							}
							if (found) break;
						}
						if (found) break;
					}

					// 如果没有找到，添加到需要翻译的列表
					if (!found) {
						wordsToTranslate.push(word);
					}
				}

				// 如果有需要翻译的单词，使用翻译服务
				if (wordsToTranslate.length > 0) {
					console.log('需要翻译的单词:', wordsToTranslate);

					// 使用翻译服务翻译单词列表
					// 翻译服务会自动处理缓存和本地存储
					const translations = await translator.translateWords(wordsToTranslate, 'en2zh');

					// 将翻译结果保存到缓存
					for (let i = 0; i < wordsToTranslate.length; i++) {
						this.translationCache[wordsToTranslate[i]] = translations[i];
					}

					console.log('翻译成功，结果:', translations);
				}

				// 确保所有单词都有翻译
				for (const word of lowerWords) {
					if (!this.translationCache[word]) {
						this.translationCache[word] = word; // 如果没有翻译，使用原单词
					}
				}

				console.log('所有单词的翻译结果:', this.translationCache);
			} catch (error) {
				console.error('批量翻译失败:', error);

				// 确保所有单词都有翻译，即使是失败的情况
				for (const word of words) {
					const lowerWord = word.toLowerCase();
					if (!this.translationCache[lowerWord]) {
						this.translationCache[lowerWord] = '翻译失败';
					}
				}
			} finally {
				// 隐藏加载提示
				uni.hideLoading();
			}
		},

		// 修改开始听写方法
		// 从本地存储中预加载翻译
		preloadTranslationsFromStorage() {
			try {
				// 如果不是英文模式，不需要翻译
				if (!this.isEnglishMode) return;

				// 分割当前输入的单词
				const words = this.inputWords.split(/[,，\s]+/).filter(word => word.trim());
				const lowerWords = words.map(word => word.toLowerCase());

				// 先尝试从 gradeData 中查找翻译
				for (const word of lowerWords) {
					// 如果已经有缓存，跳过
					if (this.translationCache[word]) continue;

					// 在 gradeData 中查找
					let found = false;
					for (const grade in gradeData) {
						for (const semester in gradeData[grade]) {
							for (const unit in gradeData[grade][semester]) {
								const unitWords = gradeData[grade][semester][unit];
								const wordData = unitWords.find(w => w.en.toLowerCase() === word);
								if (wordData) {
									// 如果找到了，缓存这个翻译
									this.translationCache[word] = wordData.zh;
									found = true;
									break;
								}
							}
							if (found) break;
						}
						if (found) break;
					}
				}

				// 检查是否所有单词都已有翻译
				const untranslatedWords = lowerWords.filter(word => !this.translationCache[word]);

				// 如果还有未翻译的单词，使用翻译服务
				if (untranslatedWords.length > 0) {
					console.log('有未翻译的单词，使用翻译服务:', untranslatedWords);
					this.preTranslateAllWords(words);
				} else {
					console.log('所有单词都已有翻译');
				}
			} catch (error) {
				console.error('预加载翻译失败:', error);
				// 如果预加载失败，使用翻译服务
				const words = this.inputWords.split(/[,，\s]+/).filter(word => word.trim());
				this.preTranslateAllWords(words);
			}
		},

		async startDictation(isReset = false) {
			if (this.isPlaying) return;

			// 检查是否可以开始听写
			if (!this.canStartDictation) {
				uni.showToast({
					title: '请选择要听写的词语或错题',
					icon: 'none'
				});
				return;
			}

			// 如果有输入词语，验证词组个数
			if (this.inputWords.trim()) {
				const isVip = userAuth.isVipUser();
				const validationResult = dictationValidator.validateWords(this.inputWords, isVip);
				if (!validationResult.isValid) {
					uni.showToast({
						title: validationResult.message,
						icon: 'none',
						duration: 3000
					});
					return;
				}
				this.wordsList = validationResult.words;
			} else {
				// 如果没有输入词语，使用错题列表
				this.wordsList = this.currentErrorWords.map(ew => ew.word);
			}

			// 保存原始顺序
			this.originalWordsList = [...this.wordsList];

			// 如果启用了随机，打乱顺序
			if (this.isRandom) {
				this.shuffleArray(this.wordsList);
			}



			// 修改：在非重置模式下，如果启用了错题混入且有错题，就添加错题
			if (!isReset && this.errorBookEnabled && this.currentErrorWords.length > 0) {
				const normalWordsCount = this.wordsList.length;
				const remainingSlots = 30 - normalWordsCount;

				if (remainingSlots > 0) {
					const errorWordsToAdd = this.currentErrorWords
						.slice(0, remainingSlots)
						.map(ew => ew.word)
						.filter(word => !this.wordsList.includes(word));

					this.wordsList = [...this.wordsList, ...errorWordsToAdd];

					// 添加提示
					if (errorWordsToAdd.length > 0) {
						uni.showToast({
							title: `已添加${errorWordsToAdd.length}个错题进行听写`,
							icon: 'none',
							duration: 2000
						});
					}
				}
			}

			// 确保总词语数不超过30个
			if (this.wordsList.length > 30) {
				this.wordsList = this.wordsList.slice(0, 30);
			}


			// 如果是英文模式，预先翻译所有单词
			if (this.isEnglishMode) {
					await this.preTranslateAllWords(this.wordsList);
				}

			// 标记听写开始，记录使用次数
			userAuth.markDictationStart();

			// 重置听写结果
			this.resultList = [];
			this.currentIndex = 0;
			this.completedCount = 0;
			this.startTime = Date.now();
			this.isPlaying = true;
			this.isDictating = true;
			this.showResult = false;

			// 播放开始提示音
			audioSpeech.speak({
				text: '开始听写',
				languageType: 'zh'
			}).then(() => {
				if (this.isPageActive) {
					this.dictateCurrentWord();
				}
			}).catch(() => {
				if (this.isPageActive) {
					this.dictateCurrentWord();
				}
			});
		},

		// 修改听写当前单词方法
		dictateCurrentWord() {
			if (!this.isPageActive || !this.isPlaying) return;

			if (this.currentIndex >= this.wordsList.length) {
				this.finishDictation();
				return;
			}

			const currentWord = this.wordsList[this.currentIndex];
			this.currentWord = currentWord;

			// 添加当前单词到结果列表，并标记是否为错题本的词
			// 只有在启用了错题本混入时，才检查是否是错题本中的词
			const isErrorWord = this.errorBookEnabled ?
				this.currentErrorWords.some(ew => ew.word === currentWord) : false;

			// 检查是否已在错题本中
			const isInErr = errorBookManager.isInErrorBook(currentWord);

			this.resultList.push({
				word: currentWord,
				dictated: true,
				pauseTime: 0,
				isErrorWord: isErrorWord,
				isInErr: isInErr // 添加是否在错题本中的状态
			});

			// 根据模式获取拼音或翻译
			if (!this.isEnglishMode) {
				// 中文模式：获取拼音
				this.currentPinyin = pinyinPro.pinyin(currentWord);
				this.currentTranslation = '';
			} else {
				// 英文模式：获取中文释义
				this.currentPinyin = '';
				// 直接调用 findTranslation，不需要等待，因为翻译已经预加载
				this.findTranslation(currentWord);
			}

			// 更新已完成数量
			this.completedCount = this.currentIndex + 1;

			// 朗读当前词语两遍
			this.speakTwice(currentWord);
		},

		// 添加查找翻译的方法
		async findTranslation(word) {
			// 首先检查本地缓存
			const lowerWord = word.toLowerCase();
			if (this.translationCache[lowerWord]) {
				console.log('使用本地缓存的翻译:', {
					word,
					translation: this.translationCache[lowerWord]
				});
				this.currentTranslation = this.translationCache[lowerWord];
				return;
			}

			// 如果没有本地缓存，遍历 gradeData 查找单词的中文释义
			let found = false;
			for (const grade in gradeData) {
				for (const semester in gradeData[grade]) {
					for (const unit in gradeData[grade][semester]) {
						const words = gradeData[grade][semester][unit];
						const wordData = words.find(w => w.en.toLowerCase() === lowerWord);
						if (wordData) {
							this.currentTranslation = wordData.zh;
							// 将结果保存到缓存
							this.translationCache[lowerWord] = wordData.zh;
							found = true;
							break;
						}
					}
					if (found) break;
				}
				if (found) break;
			}

			// 如果未找到中文释义
			if (!found) {
				// 由于我们已经在听写开始前预先翻译了所有单词
				// 这里只是一个备用方案，正常情况下不应该执行到这里
				console.warn('本地缓存中没有找到单词的翻译:', word);

				// 尝试使用翻译服务
				try {
					// 设置临时提示
					this.currentTranslation = '正在翻译中...';

					// 使用翻译服务翻译单词
					// 翻译服务会自动处理缓存和本地存储
					const translation = await translator.translateWord(word, 'en2zh');

					// 更新翻译结果
					if (translation) {
						this.currentTranslation = translation;
						// 将结果保存到本地缓存
						this.translationCache[lowerWord] = translation;
						console.log('单词翻译成功并缓存:', {
							word,
							translation: translation
						});
					} else {
						this.currentTranslation = '未找到中文释义';
					}
				} catch (error) {
					console.error('单词翻译失败:', error);
					this.currentTranslation = '翻译失败，请重试';
				}
			}
		},

		// 修改朗读两遍的方法
		speakTwice(word) {
			this.isReading = true; // 标记正在朗读
			this.countdown = this.isCustomInterval ?
				Math.max(3, word.length * this.secondsPerChar) :
				this.intervalTime; // 初始化倒计时时间

			// 第一遍朗读
			this.speakWord(word);

			// 1秒后朗读第二遍
			setTimeout(() => {
				this.speakWord(word);
				// 第二遍朗读完后开始倒计时
				setTimeout(() => {
					this.isReading = false; // 结束朗读状态
					this.startCountdown();
				}, 1000);
			}, 1500);
		},

		// 修改暂停/继续方法
		togglePause() {
			if (!this.isPageActive) return;

			if (this.isPaused) {
				// 继续听写
				this.isPaused = false;
				this.isPlaying = true;
				this.totalPauseTime += this.currentPauseTime;
				this.currentPauseTime = 0;
				this.pauseStartTime = 0;

				// 更新当前单词的暂停时间
				if (this.resultList.length > 0) {
					const currentResult = this.resultList[this.resultList.length - 1];
					currentResult.pauseTime = this.totalPauseTime;
				}

				// 从记录的剩余时间继续倒计时
				this.startCountdown();
			} else {
				// 暂停听写
				this.isPaused = true;
				this.isPlaying = false;
				this.pauseStartTime = Date.now();
				this.currentPauseTime = 0;
				this.remainingTime = this.countdown; // 记录当前剩余时间
				this.startPauseTimer();
				audioSpeech.stop();
			}
		},

		// 添加暂停计时器方法
		startPauseTimer() {
			if (this.pauseTimer) {
				clearInterval(this.pauseTimer);
			}
			this.currentPauseTime = 0;
			this.pauseTimer = setInterval(() => {
				this.currentPauseTime++;
			}, 1000);
		},
		stopFn() {
			this.isPlaying = false;
			this.isPaused = false;
			this.currentPauseTime = 0;
			this.pauseStartTime = 0;
			this.totalPauseTime = 0;
			this.cleanupResources();
			audioSpeech.stop();

			// 创建已添加词语的集合
			const dictatedWords = new Set(this.resultList.map(item => item.word));

			// 添加未听写的普通词语
			this.wordsList.slice(this.currentIndex).forEach(word => {
				if (!dictatedWords.has(word)) {
					// 只有在启用了错题本混入时，才检查是否是错题本中的词
					const isErrorWord = this.errorBookEnabled ?
						this.currentErrorWords.some(ew => ew.word === word) : false;

					this.resultList.push({
						word: word,
						dictated: false,
						pauseTime: 0,
						isErrorWord: isErrorWord
					});
					// 将新添加的词语加入集合
					dictatedWords.add(word);
				}
			});

			// 添加未听写的错题本词语（只有在启用了错题本混入时才添加）
			if (this.errorBookEnabled) {
				this.currentErrorWords.forEach(errorWord => {
					if (!dictatedWords.has(errorWord.word)) {
						this.resultList.push({
							word: errorWord.word,
							dictated: false,
							pauseTime: 0,
							isErrorWord: true
						});
					}
				});
			}

			// 显示结果
			this.isDictating = false;
			this.showResult = true;

			console.log('听写结果列表:', {
				total: this.resultList.length,
				errorWords: this.resultList.filter(item => item.isErrorWord).length,
				normalWords: this.resultList.filter(item => !item.isErrorWord).length,
				words: this.resultList.map(item => ({
					word: item.word,
					isError: item.isErrorWord,
					dictated: item.dictated
				}))
			});
		},
		// 修改停止听写方法
		async stopDictation() {
			if (!this.isPageActive) return;
			this.togglePause()

			uni.showModal({
				title: '提示',
				content: '确定要停止听写吗？',
				success: (res) => {
					if (res.confirm) {
						this.stopFn();
					}else{
						this.togglePause()
					}
				}
			});


		},

		// 修改完成听写方法
		async finishDictation() {
			// 播放结束提示音
			await audioSpeech.speak({
				text: '听写完成',
				languageType: 'zh'
			});

			this.stopFn();

			// 分离常规听写和错题本听写的结果
			this.resultList = this.resultList.map(result => {
				// 只有在启用了错题本混入时，才检查是否是错题本中的词
				const isErrorWord = this.errorBookEnabled ?
					this.currentErrorWords.some(ew => ew.word === result.word) : false;

				return {
					...result,
					isErrorWord: isErrorWord
				};
			});

			// 添加未听写的错题本词语到结果列表（只有在启用了错题本混入时才添加）
			if (this.errorBookEnabled) {
				const dictatedWords = new Set(this.resultList.map(item => item.word));
				this.currentErrorWords.forEach(errorWord => {
					if (!dictatedWords.has(errorWord.word)) {
						this.resultList.push({
							word: errorWord.word,
							dictated: false,
							pauseTime: 0,
							isErrorWord: true
						});
					}
				});
			}

			console.log('处理后的结果列表:', {
				total: this.resultList.length,
				errorWords: this.resultList.filter(item => item.isErrorWord).length,
				normalWords: this.resultList.filter(item => !item.isErrorWord).length,
				fullList: this.resultList
			});

			// 显示结果
			this.showResult = true;
			this.isDictating = false;
		},

		// 随机打乱数组
		shuffleArray(array) {
			for (let i = array.length - 1; i > 0; i--) {
				const j = Math.floor(Math.random() * (i + 1));
				[array[i], array[j]] = [array[j], array[i]];
			}
		},

		// 重置听写
		resetDictation() {
			if (!this.isPageActive) return;
			if(this.selectedWords.length <= 0){
				uni.showToast({
					title: '请先选择要听写的词语',
					icon: 'none',
					duration: 2000
				});
				return;
			}

			// 检查选中的词语中是否包含错题本的词
			const hasErrorWords = this.selectedWords.some(word => this.currentErrorWords.some(ew => ew.word === word));

			// 如果包含错题本的词，自动开启错题本模式
			if (hasErrorWords) {
				this.errorBookEnabled = true;
				uni.showToast({
					title: '已自动开启错题本模式',
					icon: 'none',
					duration: 2000
				});
			}

			uni.showModal({
				title: '提示',
				content: '确定要重新听写吗？',
				success: (res) => {
					if (res.confirm) {
						// 去重处理选中的词语
						const selectedWords = [...new Set(this.selectedWords)];
						this.inputWords = selectedWords.join(',');
						this.startDictation(true);
					}
				}
			});
		},

		// 清理资源
		cleanupResources() {
			if (this.timer) {
				clearInterval(this.timer);
				this.timer = null;
			}

			audioSpeech.stop();

			if (this.startSound) {
				this.startSound = null;
			}
			if (this.endSound) {
				this.endSound = null;
			}

			if (this.pauseTimer) {
				clearInterval(this.pauseTimer);
				this.pauseTimer = null;
			}
		},

		// 切换是否显示当前词语
		// toggleShowWord(e) {
		// 	this.showCurrentWord = e.detail.value;
		// },

		// 切换是否随机听写
		toggleRandom(e) {
			this.isRandom = e.detail.value;
		},

		// 修改切换英语模式的方法
		toggleEnglishMode(e) {
			const newMode = e.detail.value;
			this.isEnglishMode = newMode;

			// 根据模式调整每字秒数
			if (this.isEnglishMode) {
				this.secondsPerChar = 1;
			} else {
				this.secondsPerChar = 3;
			}

			// 更新当前错题列表
			this.updateCurrentErrorWords();

			// 更新分享链接
			this.updateSharePath();
		},

		// 更新当前模式的错题列表方法
		updateCurrentErrorWords() {
			const type = this.isEnglishMode ? 'english' : 'chinese';

			// 使用 errorBookManager 获取错题列表
			this.currentErrorWords = errorBookManager.getErrorWords(type);

			// 同步到组件中
			this.errorWords[type] = [...this.currentErrorWords];

			console.log('当前错题本数据:', {
				mode: this.isEnglishMode ? '英文' : '中文',
				errorWords: this.currentErrorWords
			});
		},

		// 选择间隔时间
		selectInterval(value) {
			// 只在非听写状态或暂停状态下允许修改
			if (this.isDictating && !this.isPaused) return;

			this.intervalTime = value;
			this.isCustomInterval = false;

			// 如果在暂停状态下修改，重新计算倒计时时间
			if (this.isDictating && this.isPaused) {
				this.updateCountdown();
			}
		},

		// 返回首页
		goBack() {
			console.log("back")
			this.stopFn();
			uni.reLaunch({
				url: '/pages/index/index'
			});
		},

		// 切换是否使用自定义间隔时间
		toggleCustomInterval(value) {
			// 只在非听写状态或暂停状态下允许修改
			if (this.isDictating && !this.isPaused) return;

			this.isCustomInterval = value;

			// 如果在暂停状态下修改，重新计算倒计时时间
			if (this.isDictating && this.isPaused) {
				this.updateCountdown();
			}
		},

		// 调整每字秒数
		adjustSecondsPerChar(delta) {
			// 只在非听写状态或暂停状态下允许修改
			if (this.isDictating && !this.isPaused) return;

			const newValue = this.secondsPerChar + delta;
			if (newValue >= 1 && newValue <= 10) {
				this.secondsPerChar = newValue;

				// 如果在暂停状态下修改，重新计算倒计时时间
				if (this.isDictating && this.isPaused) {
					this.updateCountdown();
				}
			}
		},

		// 修改倒计时方法
		startCountdown() {
			// 清除可能存在的定时器
			if (this.timer) {
				clearInterval(this.timer);
			}

			// 启动新的定时器
			this.timer = setInterval(() => {
				if (this.isPaused) return; // 暂停时不继续倒计时

				if (this.countdown > 0) {
					this.countdown--;
				}

				if (this.countdown <= 0) {
					clearInterval(this.timer);
					this.currentIndex++;
					if (this.currentIndex < this.wordsList.length) {
						this.dictateCurrentWord();
					} else {
						this.finishDictation();
					}
				}
			}, 1000);
		},

		// 添加更新倒计时时间的方法
		updateCountdown() {
			if (this.isCustomInterval) {
				const wordLength = this.currentWord.length;
				this.countdown = Math.max(3, wordLength * this.secondsPerChar);
			} else {
				this.countdown = this.intervalTime;
			}
		},

		// 清理缓存方法
		clearAudioCache() {
			this.audioCache.clear();
			uni.removeStorageSync('audioUrlCache');
		},

		// 修改打乱单词顺序的方法
		shuffleWords(words) {
			const wordArray = words.split(/[,，\n]+/).map(word => word.trim()).filter(word => word);
			for (let i = wordArray.length - 1; i > 0; i--) {
				const j = Math.floor(Math.random() * (i + 1));
				[wordArray[i], wordArray[j]] = [wordArray[j], wordArray[i]];
			}
			return wordArray.join(',');
		},

		// 添加获取年级数据的方法
		getGradeData(grade) {
			return gradeData[grade];
		},

		// 修改获取单词的方法
		getWords(options) {
			const words = [];
			const gradeData = this.getGradeData(options.grade);

			if (gradeData && gradeData[options.grade] && gradeData[options.grade][options.semester]) {
				const units = JSON.parse(options.units);
				units.forEach(unit => {
					if (gradeData[options.grade][options.semester][unit]) {
						const unitWords = gradeData[options.grade][options.semester][unit];
						words.push(...unitWords);
					}
				});
			}

			return words;
		},
		shareTest() {
			// 如果没有输入词语，不允许分享
			if (!this.inputWords.trim()) {
				uni.showToast({
					title: '请先输入要分享的词语',
					icon: 'none',
					duration: 2000
				});
				return;
			}

			// 验证词组
			const isVip = userAuth.isVipUser();
			const validationResult = dictationValidator.validateWords(this.inputWords, isVip);
			if (!validationResult.isValid) {
				uni.showToast({
					title: validationResult.message,
					icon: 'none',
					duration: 3000
				});
				return;
			}
		},
		async handleInputChange(e) {
			// 更新分享链接
			this.updateSharePath();

			// 生成分享图片
			if (this.inputWords.trim()) {
				try {
					// 使用更新后的 generateShareImage 函数，传递配置参数
					const imageUrl = await generateShareImage(this.inputWords, {
						title: '今日听写',  // 使用默认标题
						showDate: true,     // 显示日期
						delimiter: '、'      // 使用中文顿号作为分隔符
					});
					console.log("imageUrl",imageUrl);
					this.share.imageUrl = imageUrl;
				} catch (error) {
					console.error('生成分享图片失败:', error);
					// 如果生成失败，使用默认图片
					this.share.imageUrl = '/static/share-image.png';
				}
			} else {
				// 如果没有输入内容，使用默认图片
				this.share.imageUrl = '/static/share-image.png';
			}
		},
		// 修改分享给好友的配置
		// onShareAppMessage(res) {
		// 	console.log('触发分享方法', res);


		// 	return {
		// 		title: '来和听写吧',
		// 		path: path,
		// 		imageUrl: '/static/share-image.png'
		// 	};
		// },
		// 修改分享到朋友圈的配置
		// onShareTimeline() {
		// 	const words = encodeURIComponent(this.inputWords);
		// 	return {
		// 		title: '来和我一起听写吧',
		// 		query: `words=${words}`,
		// 		imageUrl: '/static/share-image.png'
		// 	};
		// },

		// 添加全选/取消全选方法
		toggleSelectAll() {
			if (this.selectedWords.length === this.wordsList.length) {
				this.selectedWords = [];
			} else {
				this.selectedWords = [...this.wordsList];
			}
		},

		// 添加单个选择方法
		toggleSelect(word) {
			const index = this.selectedWords.indexOf(word);
			if (index === -1) {
				this.selectedWords.push(word);
			} else {
				this.selectedWords.splice(index, 1);
			}
		},

		// 初始化错题本
		initErrorBook() {
			// 使用 errorBookManager 初始化错题本
			errorBookManager.initErrorBook();

			// 将 errorBookManager 中的数据同步到组件中
			this.errorWords = {
				chinese: errorBookManager.getErrorWords('chinese'),
				english: errorBookManager.getErrorWords('english')
			};

			// 更新当前错题列表
			this.updateCurrentErrorWords();
		},

		// 修改切换正确标记的方法
		toggleCorrect(word) {
			console.log('触发 toggleCorrect:', {
				word,
				mode: this.isEnglishMode ? '英文' : '中文'
			});

			// 确保 startTime 不是 undefined
			const dictationId = this.startTime ? this.startTime.toString() : Date.now().toString();

			// 使用 errorBookManager 的 toggleCorrect 方法
			const result = errorBookManager.toggleCorrect(word, dictationId);

			console.log('切换正确标记结果:', result);

			// 更新组件中的错题本数据
			this.updateCurrentErrorWords();

			// 显示提示
			uni.showToast({
				title: result.message,
				icon: 'none',
				duration: 2000
			});

			// 强制更新视图
			this.$forceUpdate();

			return result;
		},

		// 从错题本移除
		removeFromErrorBook(word) {
			const type = this.isEnglishMode ? 'english' : 'chinese';
			const removed = errorBookManager.removeFromErrorBook(word, type);
			this.updateCurrentErrorWords();

			// 更新结果列表中的 isInErr 状态
			const wordItem = this.resultList.find(item => item.word === word);
			if (wordItem && removed) {
				wordItem.isInErr = false;
				console.log('从错题本移除并更新状态:', {
					word,
					isInErr: wordItem.isInErr
				});
			}

			// 显示提示
			if (removed) {
				uni.showToast({
					title: '已从错题本移除',
					icon: 'none',
					duration: 2000
				});
			}

			return removed;
		},

		// 保存错题本到本地存储
		saveErrorBook() {
			// 使用 errorBookManager 保存错题本
			return errorBookManager.saveErrorBook();
		},

		// 修改切换错题混入开关的方法
		toggleErrorBookEnabled(e) {
			const enabled = e.detail.value;

			// 计算当前普通听写词语数量
			const normalWordsCount = this.wordsList.filter(word =>
				!this.currentErrorWords.some(ew => ew.word === word)
			).length;

			if (enabled) {
				if (normalWordsCount >= 30) {
					// 如果普通听写词语已达到30个，显示提示并阻止开启
					uni.showToast({
						title: '听写任务已经满30个词语了，无法添加错题',
						icon: 'none',
						duration: 2000
					});
					this.errorBookEnabled = false;
					return;
				}

				// 计算还可以添加多少个错题
				const remainingSlots = 30 - normalWordsCount;
				// 从错题本中选择词语补充到30个
				const errorWordsToAdd = this.currentErrorWords.slice(0, remainingSlots);

				if (errorWordsToAdd.length > 0) {
					uni.showToast({
						title: `已添加${errorWordsToAdd.length}个错题进行听写`,
						icon: 'none',
						duration: 2000
					});
				}
			}

			this.errorBookEnabled = enabled;
		},

		// 检查单词是否在错题本中
		isInErrorBook(word) {
			console.log('触发 isInErrorBook:', {
				word,
				mode: this.isEnglishMode ? '英文' : '中文'
			});
			return errorBookManager.isInErrorBook(word);
		},

		// 检查单词是否在待添加列表中
		isPendingErrorWord(word) {
			return this.pendingErrorWords.includes(word);
		},

		// 保存待添加的错题到错题本
		savePendingErrorWords() {
			if (this.pendingErrorWords.length === 0) return;

			console.log('保存待添加的错题:', this.pendingErrorWords);

			// 遍历待添加列表，将词语添加到错题本中
			this.pendingErrorWords.forEach(word => {
				// 使用 errorBookManager 添加到错题本
				const result = errorBookManager.addToErrorBook(word);
				console.log('添加到错题本结果:', {
					word,
					result
				});
			});

			// 更新当前错题列表
			this.updateCurrentErrorWords();

			// 清空待添加列表
			this.pendingErrorWords = [];
		},

		// 切换错题标记（修改为使用本地暂存）
		toggleErrorMark(word) {
			// 检查词语是否在待添加列表中
			const index = this.pendingErrorWords.indexOf(word);
			let message = '';

			if (index > -1) {
				// 如果在待添加列表中，则移除
				this.pendingErrorWords.splice(index, 1);
				message = '已取消添加到错题本';
			} else {
				// 如果不在待添加列表中，则添加
				this.pendingErrorWords.push(word);
				message = '已添加到错题本（将在退出页面时保存）';
			}

			// 更新结果列表中的状态（不再使用isInErr，因为这个状态现在只表示是否在errorBookManager中）
			const wordItem = this.resultList.find(item => item.word === word);
			if (wordItem) {
				console.log('更新词语待添加状态:', {
					word,
					isPending: this.isPendingErrorWord(word)
				});
			}

			// 显示提示
			uni.showToast({
				title: message,
				icon: 'none',
				duration: 2000
			});

			return {
				success: true,
				message: message,
				inErrorBook: false
			};
		},

		// 获取单词的正确次数
		getCorrectCount(word) {
			console.log('触发 getCorrectCount:', {
				word,
				type: this.isEnglishMode ? 'english' : 'chinese'
			});

			// 使用 errorBookManager 获取正确次数
			const count = errorBookManager.getCorrectCount(word);

			console.log('获取正确次数:', {
				word,
				count,
				currentDictationId: this.startTime
			});

			return count;
		},

		// 判断词语是否在当前听写中被标记为正确
		isMarkedCorrectInCurrentDictation(word) {
			const type = /^[a-zA-Z\s]+$/.test(word) ? 'english' : 'chinese';
			const dictationId = this.startTime ? this.startTime.toString() : Date.now().toString();

			// 获取错题记录
			const errorWord = this.errorWords[type].find(w => w.word === word);

			// 如果有错题记录，并且在当前听写中被标记为正确，返回 true
			return errorWord && errorWord.lastDictationId === dictationId;
		},

		// 修改添加到错题本的方法
		addToErrorBook(word) {
			// 使用 errorBookManager 添加到错题本
			const result = errorBookManager.addToErrorBook(word);

			// 更新当前错题列表
			this.updateCurrentErrorWords();

			// 更新结果列表中的 isInErr 状态
			const wordItem = this.resultList.find(item => item.word === word);
			if (wordItem && result.success) {
				wordItem.isInErr = true;
				console.log('添加到错题本并更新状态:', {
					word,
					isInErr: wordItem.isInErr
				});
			}

			// 添加提示
			if (result.success) {
				uni.showToast({
					title: result.message,
					icon: 'none',
					duration: 2000
				});
			}

			return result;
		},

		// 修改更新分享路径的方法
		updateSharePath() {
			let path = '/pages/dictation/index';
			let encodedWords = '';

			if (this.inputWords.trim()) {
				// 对输入内容进行预处理
				const processedWords = this.inputWords
					.replace(/\s+/g, ' ') // 将多个空格替换为单个空格
					.trim();

				// 使用 encodeURIComponent 对参数进行编码
				encodedWords = encodeURIComponent(processedWords);

				// 使用 updateUrlParam 更新参数
				path = updateUrlParam(path, 'words', encodedWords);
			}
			path = updateUrlParam(path, 'isEn', this.isEnglishMode);
			this.share.path = path;

			console.log('更新后的分享路径:', {
				path: this.share.path,
				words: this.inputWords,
				encodedWords: encodedWords,
				isEnglishMode: this.isEnglishMode
			});
		},
	}
}
</script>

<style scoped lang="scss">
	.toolbox{
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		.p_left{
			display: flex;
			flex-direction: column;
			justify-content: space-between;
		}
		.p_right{
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			align-items: end;
		}
	}
	.dictation-container {
		min-height: calc(100vh - 130rpx);
		background-color: #f8f8f8;
		padding: 30rpx;
	}



	.title {
		font-size: 44rpx;
		font-weight: 600;
		color: #393C98;
	}

	.input-area, .dictation-area, .result-area {
		background-color: #ffffff;
		border-radius: 24rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
	}
	.top-bar-title{
		font-size: 24rpx;
		color: #DF6B67;
		margin-bottom: 20rpx;
		//空两格
	}
	.input-title {
		font-size: 32rpx;
		color: #333;
		margin-bottom: 20rpx;
	}

	.tip {
		font-size: 24rpx;
		color: #999;
		margin-left: 10rpx;
	}

	.textarea-box {
		background-color: #f8f8f8;
		border-radius: 12rpx;
		padding: 20rpx;
		margin-bottom: 20rpx;
	}

	textarea {
		width: 100%;
		min-height: 200rpx;
		max-height: 200rpx;
		font-size: 28rpx;
	}

	.option-item {
		display: flex;
		align-items: center;
		margin-top: 20rpx;
	}

	.option-item text {
		font-size: 28rpx;
		color: #666;
		margin-left: 10rpx;
	}

	.progress-info {
		margin-bottom: 30rpx;
	}

	.progress-text {
		font-size: 32rpx;
		color: #333;
		margin-bottom: 15rpx;
		display: block;
	}

	.progress-bar {
		height: 16rpx;
		background-color: #e0e0e0;
		border-radius: 8rpx;
		overflow: hidden;
	}

	.progress-inner {
		height: 100%;
		background-color: #393C98;
		border-radius: 8rpx;
		transition: width 0.3s;
	}

	/* 修改后的听写卡片样式 */
	.dictation-card {
		background-color: #fff;
		border-radius: 24rpx;
		padding: 40rpx;
		margin: 30rpx 0;
		box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
		text-align: center;
		animation: fadeIn 0.3s ease-in-out;
	}

	@keyframes fadeIn {
		from { opacity: 0; transform: translateY(-20rpx); }
		to { opacity: 1; transform: translateY(0); }
	}

	.pinyin-display {
		margin-bottom: 30rpx;
		min-height: 60rpx;

		text {
			font-size: 56rpx;
			color: #8B648B;
			font-weight: 500;
			letter-spacing: 2rpx;
		}

		.translation-text {
			font-size: 40rpx; // 中文释义字体稍小
			color: #8B648B;
			font-weight: 400;
		}
	}

	.current-word {
		background-color: #f0f0f0;
		padding: 40rpx;
		border-radius: 16rpx;
		margin: 20rpx 0;
		text-align: center;
	}

	.current-word text {
		font-size: 60rpx;
		font-weight: 500;
		color: #333;
	}

	.countdown {
		text-align: center;
		font-size: 28rpx;
		color: #666;
		margin-top: 20rpx;
	}

	.dictation-options {
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 30rpx;
	}

	.dictation-options text {
		font-size: 28rpx;
		color: #666;
		margin-left: 10rpx;
	}

	/* 结果区域样式 */
	.result-title {
		font-size: 36rpx;
		font-weight: 500;
		color: #333;
		margin-bottom: 30rpx;
		text-align: center;
	}

	.result-list {
		max-height: calc(100vh - 460rpx);
		overflow-y: auto;
	}

	.result-item {
		display: flex;
		align-items: center;
		padding: 20rpx;
		border-bottom: 1px solid #f0f0f0;
	}

	.result-index {
		width: 60rpx;
		font-size: 28rpx;
		color: #999;
		padding-top: 6rpx;
	}

	.result-word-container {
		flex: 1;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		gap: 8rpx;
	}

	.result-word {
		font-size: 32rpx;
		color: #333;
		margin-right: 20rpx;
	}

	.pause-time {
		font-size: 24rpx;
		color: #8B648B;
		background-color: rgba(139, 100, 139, 0.1);
		padding: 4rpx 12rpx;
		border-radius: 8rpx;
		display: inline-block;
	}

	.result-status {
		font-size: 24rpx;
		color: #ff6b6b;
		padding: 4rpx 12rpx;
		background-color: rgba(255, 107, 107, 0.1);
		border-radius: 8rpx;
		margin-left: auto;
	}

	.not-dictated {
		opacity: 0.6;
	}

	.result-summary {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-top: 30rpx;
		padding: 20rpx;
		background-color: #f8f8f8;
		border-radius: 12rpx;
	}

	.result-summary text {
		font-size: 28rpx;
		color: #666;
	}

	.action-buttons {
		/*margin: 40rpx 0;*/
	}

	.action-btn {
		font-size: 32rpx;
		height: 90rpx;
		line-height: 90rpx;
	}

	button[type="primary"] {
		background-color: #393C98 !important;
	}

	.button-group {
		display: flex;
		justify-content: space-between;
		gap: 20rpx;
	}

	.button-group .action-btn {
		flex: 1;
	}

	.pause-btn, .stop-btn {
		width: 48% !important;
	}

	/* 新增样式 */
	.option-title {
		font-size: 28rpx;
		color: #666;
		margin: 30rpx 0 15rpx;
	}

	.time-options {
		display: flex;
		justify-content: space-between;
		margin-bottom: 20rpx;
	}

	.time-option {
		flex: 1;
		text-align: center;
		padding: 15rpx 0;
		margin: 0 10rpx;
		border-radius: 8rpx;
		background-color: #f0f0f0;
		transition: all 0.3s;
	}

	.time-option.active {
		background-color: #393C98;
	}

	.time-option.active text {
		color: #fff;
	}

	.time-option text {
		font-size: 28rpx;
		color: #666;
	}

	.result-translation {
		font-size: 28rpx;
		color: #8B648B;
		margin-left: 15rpx;
		padding: 4rpx 12rpx;
		background-color: #f0f0f0;
		border-radius: 8rpx;
	}

	/* 为新增功能添加样式 */
	.per-char-control {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx;
		background-color: #f9f9f9;
		border-radius: 12rpx;
		margin: 10rpx 0 30rpx;
	}

	.per-char-label {
		font-size: 28rpx;
		color: #666;
	}

	.per-char-adjuster {
		display: flex;
		align-items: center;
	}

	.adjuster-btn {
		width: 60rpx;
		height: 60rpx;
		border-radius: 30rpx;
		background-color: #e0e0e0;
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 0 15rpx;
	}

	.adjuster-btn:active {
		background-color: #d0d0d0;
	}

	.adjuster-icon {
		font-size: 36rpx;
		color: #333;
		line-height: 1;
	}

	.seconds-display {
		font-size: 32rpx;
		color: #333;
		font-weight: 500;
		width: 80rpx;
		text-align: center;
	}

	/* 添加新的样式 */
	.interval-settings {
		background-color: #fff;
		padding: 20rpx;
		border-radius: 12rpx;
		margin: 20rpx 0;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.pause-info {
		background-color: rgba(57, 60, 152, 0.1);
		padding: 20rpx;
		border-radius: 12rpx;
		margin: 20rpx 0;
		text-align: center;
	}

	.pause-timer {
		font-size: 28rpx;
		color: #393C98;
		font-weight: 500;
	}

	/* 修改分享按钮样式 */
	.share-test-btn {
		background-color: #393C98 !important;
		color: #ffffff !important;
		border-radius: 12rpx;
		font-size: 28rpx;
		height: 80rpx;
		padding: 0 30rpx;
		border: none;
		margin: 0;

		.btn-content {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 100%;

			text {
				color: #ffffff;
				margin-left: 10rpx;
			}
		}

		.share-icon {
			width: 32rpx;
			height: 32rpx;
		}

		&:disabled {
			opacity: 0.6;
			background-color: #999 !important;
		}
	}

	/* 删除旧的保存按钮样式 */
	.save-btn {
		display: none;
	}

	/* 添加隐藏 canvas 样式 */
	.hidden-canvas {
		position: fixed;
		left: -2000px;
		top: 0;
		width: 100px;
		height: 100px;
		z-index: -1;
	}

	/* 添加新样式 */
	.select-all-container {
		display: flex;
		align-items: center;
		margin-right: 20rpx;
	}

	.select-all-container text {
		font-size: 28rpx;
		color: #666;
		margin-left: 10rpx;
	}

	.error-word-list {
		margin-top: 20rpx;
		border-top: 1px solid #ccc;
		padding-top: 20rpx;
	}

	.error-book-info {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.error-book-title {
		font-size: 28rpx;
		color: #666;
		font-weight: 500;
	}

	.error-book-switch {
		display: flex;
		align-items: center;
		gap: 10rpx;

		text {
			font-size: 28rpx;
			color: #666;
		}
	}

	.word-actions {
		display: flex;
		align-items: center;
		gap: 10rpx;
	}

	.error-mark {
		padding: 4rpx 16rpx;
		border-radius: 20rpx;
		font-size: 24rpx;
		color: #999;
		background-color: #f0f0f0;
		border: 1px solid #e0e0e0;

		&.active {
			color: #fff;
			background-color: #393C98;
			border-color: #393C98;
		}

		&.error-mark-disabled {
			color: #fff;
			background-color: rgba(57, 60, 152, 0.5); /* 蓝紫色背景，50%透明度 */
			border-color: rgba(57, 60, 152, 0.5);
			cursor: not-allowed;
		}
	}

	.error-book-section {
		margin-top: 30rpx;
		padding-top: 20rpx;
		border-top: 1px solid #f0f0f0;

		.error-book-title {
			font-size: 32rpx;
			color: #393C98;
			font-weight: 500;
			margin-bottom: 20rpx;
		}
	}

	.correct-marks {
		display: flex;
		gap: 6rpx;
	}

	.correct-mark {
		font-size: 24rpx;
		color: #ccc;

		&.active {
			color: #393C98;
		}
	}

	.error-item {
		background-color: rgba(57, 60, 152, 0.05);
	}

	.correct-btn {
		padding: 4rpx 16rpx;
		border-radius: 20rpx;
		font-size: 24rpx;
		color: #fff;
		background-color: #393C98;
		margin-right: 10rpx;

		&.inactive {
			opacity: 0.5;
		}
	}
</style>