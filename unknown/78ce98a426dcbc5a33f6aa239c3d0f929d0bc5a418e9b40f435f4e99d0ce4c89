/**
 * 汉字听写输入验证工具
 */
export const dictationValidator = {
  /**
   * 验证词组
   * @param {string} words - 输入的词组
   * @param {boolean} isVip - 是否是VIP用户
   * @returns {Object} - 验证结果
   */
  validateWords(words, isVip = false) {
    // 移除空白字符
    const trimmedWords = words.trim();
    
    // 分割词组（按照换行或逗号分割）
    const wordList = trimmedWords.split(/[\n,，]/g).filter(word => word.trim());
    
    // 验证词组数量
    const maxWords = isVip ? 50 : 30;
    if (wordList.length > maxWords) {
      return {
        isValid: false,
        message: `词组数量不能超过${maxWords}个,请删除${wordList.length-maxWords}个词组`
      };
    }
    
    // 验证每个词组长度
    const invalidWords = wordList.filter(word => {
      const trimmedWord = word.trim();
      // 检查是否包含中文字符
      const hasChinese = /[\u4e00-\u9fa5]/.test(trimmedWord);
      
      if (hasChinese) {
        // 如果包含中文，限制4个字符
        return trimmedWord.length > 4;
      }
      // 如果是纯英文，不限制长度
      return false;
    });
    
    if (invalidWords.length > 0) {
      return {
        isValid: false,
        message: `以下词组超过4个字：${invalidWords.join('、')}`
      };
    }
    
    // 验证通过，返回处理后的词组列表
    return {
      isValid: true,
      words: wordList,
      message: '验证通过'
    };
  },

  /**
   * 格式化词组
   * @param {string} words - 输入的词组
   * @param {boolean} isVip - 是否是VIP用户
   * @returns {string} - 格式化后的词组
   */
  formatWords(words, isVip = false) {
    const result = this.validateWords(words, isVip);
    if (result.isValid) {
      return result.words.join('\n');
    }
    return words;
  }
}; 