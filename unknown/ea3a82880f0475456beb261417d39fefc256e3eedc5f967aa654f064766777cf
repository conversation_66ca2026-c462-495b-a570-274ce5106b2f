<template>
  <view class="container">
    <view class="page-header">
      <view class="back-btn" @tap="goBack">
        <text>返回</text>
      </view>
      <view class="page-title">
        <text>腾讯云翻译</text>
      </view>
    </view>
    
    <view class="content">
      <TXTranslator />
    </view>
    
    <view class="tips-section">
      <view class="tips-title">
        <text>使用说明</text>
      </view>
      <view class="tips-content">
        <text>1. 本翻译功能基于腾讯云机器翻译API</text>
        <text>2. 支持单词翻译和文本翻译两种模式</text>
        <text>3. 单词翻译支持中英互译</text>
        <text>4. 文本翻译支持多种语言互译</text>
        <text>5. 翻译结果可一键复制</text>
      </view>
    </view>
  </view>
</template>

<script>
import TXTranslator from '@/components/TXTranslator.vue';

export default {
  components: {
    TXTranslator
  },
  data() {
    return {}
  },
  methods: {
    goBack() {
      uni.navigateBack();
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  min-height: 100vh;
  padding: 30rpx;
  background-color: #f8f8f8;
}

.page-header {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.back-btn {
  padding: 10rpx 20rpx;
  background-color: #eee;
  border-radius: 8rpx;
  margin-right: 20rpx;
  
  text {
    font-size: 28rpx;
    color: #666;
  }
}

.page-title {
  flex: 1;
  text-align: center;
  
  text {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
  }
}

.content {
  margin-bottom: 40rpx;
}

.tips-section {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.tips-title {
  margin-bottom: 20rpx;
  
  text {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }
}

.tips-content {
  display: flex;
  flex-direction: column;
  
  text {
    font-size: 28rpx;
    color: #666;
    line-height: 1.8;
  }
}
</style>
