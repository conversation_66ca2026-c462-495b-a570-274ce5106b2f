<template>
  <view class="container">
    <!-- 添加隐藏的 canvas 元素用于生成分享图片 -->
    <canvas canvas-id="shareCanvas" style="width: 400px; height: 320px; position: fixed; left: -9999px;"></canvas>
    <view class="header">

      <view class="mode-selector">
        <view
          v-for="(mode, index) in studyModes"
          :key="index"
          :class="['mode-item', currentMode === mode.value ? 'active' : '']"
          @click="switchMode(mode.value)"
        >
          {{ mode.label }}
        </view>
      </view>
      <view class="action-btns">
        <view class="test-btn" @click="goToTest">
          <text class="iconfont">去测试</text>
        </view>
        <view class="dictation-btn" @click="goDictation">
          <text class="iconfont">去听写</text>
        </view>
        <view class="share-btn">
          <button
            class="share-button"
            open-type="share"
          >
            <text class="iconfont">去分享</text>
          </button>
        </view>
      </view>
      <view class="progress">
        <text>{{ currentIndex + 1 }}/{{ wordCards.length }}</text>
      </view>
    </view>

    <view class="content">
      <swiper
        class="card-swiper"
        :current="currentIndex"
        @change="handleSwiperChange"
        :circular="false"
        :disable-touch="false"
      >
        <swiper-item v-for="(card, index) in wordCards" :key="index">
          <view
            class="card-container"
            :class="{ 'flipped': card.flipped }"
            @click="flipCard(index)"
          >
            <view class="card-front">
              <view class="card-type" v-if="false">{{ card.en.includes(' ') ? '短语' : '单词' }}</view>
              <view class="card-content" v-if="currentMode !== 'audio'">
                {{ getFrontContent(card) }}
              </view>
              <view class="card-content audio-mode" v-else>
                <text>点击播放</text>
                <view class="audio-icon">🔊</view>
              </view>
              <view class="card-tip">{{ getCardTip() }}</view>
            </view>
            <view class="card-back">
              <view class="card-content">
                <text class="back-en">{{ card.en }}</text>
                <text class="back-zh">{{ card.zh }}</text>
              </view>
              <!-- 如果单词已在错题本中，显示“已加入错题本”，否则显示“添加到错题本”按钮 -->
              <view v-if="isInErrorBook(card.en)" class="card-tip in-error-book">已加入错题本</view>
              <view v-else class="card-tip add-to-error-book" @click.stop="addToErrorBook(card, $event)">添加到错题本</view>
            </view>
          </view>
        </swiper-item>
      </swiper>
    </view>

    <view class="controls">
      <view class="control-btn" @click="prevCard" :class="{ 'disabled': currentIndex === 0 }">
        <text class="iconfont">上一个</text>
      </view>
      <view class="control-btn speak-btn" @click="speakCurrentWord">
        <text class="iconfont">朗读</text>
      </view>
      <view class="control-btn" @click="nextCard" :class="{ 'disabled': currentIndex === wordCards.length - 1 }">
        <text class="iconfont">下一个</text>
      </view>

    </view>
  </view>
</template>

<script>

import { speech, checkSpeechSupport } from '@/utils/speech.js'; // 导入封装的语音工具
import { gradeData } from '@/static/data/index.js';
import errorBookManager from '@/utils/ErrorBookManager.js'; // 导入错题本管理工具
import translator from '@/utils/translationService.js'; // 导入整合的翻译服务
import customCardStorage from '@/utils/CustomCardStorage.js'; // 导入自定义卡片存储工具
import { generateShareImage } from '@/utils/shareImage.js'; // 导入分享图片生成工具

export default {
  data() {
    return {
      grade: '',
      semester: '',
      selectedUnits: [],
      wordCards: [],
      currentIndex: 0,
      isRandom: false,
      currentMode: 'en',
      studyModes: [
        { label: '英文', value: 'en' },
        { label: '中文', value: 'zh' },
        { label: '听力', value: 'audio' }
      ],
      pageOptions: null, // 保存页面参数
      // 分享相关数据
      share: {
        title: '七宝学习助手-单词卡片',
        path: '/pages/english/wordcard/learn',
        imageUrl: '',
        desc: '快来和我一起学习单词吧',
        content: '七宝学习助手-单词卡片'
      },
      isGeneratingImage: false // 图片生成状态
    }
  },
  onLoad(options) {
    //清理之前的错误缓存
    // translator.clearTranslationCache();
    try {
      // 保存原始页面参数
      this.pageOptions = { ...options };

      // 检查是否是自定义卡片模式
      if (options.isCustom === 'true' && options.words) {
        // 自定义卡片模式
        const decodedWords = decodeURIComponent(options.words);
        const parsedWords = JSON.parse(decodedWords);

        // 使用默认的英文到中文模式
        this.currentMode = 'en';
        // 直接使用翻译 API 获取翻译结果，不从本地存储中获取
        this.initCustomWords(parsedWords);


      } else {
        // 原有的单元卡片模式
        const { grade, semester, units } = options;
        // 解码并解析 units 参数
        const decodedUnits = decodeURIComponent(units);
        const parsedUnits = JSON.parse(decodedUnits);
        this.initWords(grade, semester, parsedUnits);

      }

    } catch (error) {
      console.error('Error parsing options:', error);
      uni.showToast({
        title: '加载数据失败',
        icon: 'none'
      });
    }
  },
  onUnload() {
    // 页面卸载时停止语音
    speech.stop();
  },

  // 分享相关生命周期钩子
  onShareAppMessage() {
    // 更新分享路径
    this.updateSharePath();



    return {
      title: this.share.title,
      path: this.share.path,
      imageUrl: this.share.imageUrl,
      desc: this.share.desc,
      content: this.share.content,
      success: () => {
        console.log('分享成功');
      },
      fail: (err) => {
        console.error('分享失败:', err);
      }
    };
  },
  mounted() {
    // 检查语音支持并初始化
    checkSpeechSupport().then(supported => {
      if (!supported) {
        uni.showToast({
          title: '您的设备可能不支持语音功能',
          icon: 'none'
        });
      }
    });

    // #ifdef H5
    // 在用户第一次点击时初始化音频
    const initAudio = () => {
      speech.speak('').then(() => {
        document.removeEventListener('touchstart', initAudio);
        document.removeEventListener('click', initAudio);
      }).catch(console.error);
    };

    document.addEventListener('touchstart', initAudio, false);
    document.addEventListener('click', initAudio, false);
    // #endif
  },
  methods: {
    getGradeData(grade) {
      console.log(gradeData,grade);
      return gradeData[grade];
    },


    // 使用翻译 API 初始化自定义单词
    async initCustomWords(words) {
      try {
        if (!words || !Array.isArray(words) || words.length === 0) {
          throw new Error('单词列表不能为空');
        }

        // 显示加载提示
        uni.showLoading({
          title: '正在加载单词...',
          mask: true
        });

        // 准备卡片数据
        this.wordCards = [];

        // 始终使用英文到中文的翻译方向
        const direction = 'en2zh';

        // 确保单词是小写形式，便于比较和存储
        const lowerWords = words.map(word => typeof word === 'string' ? word.toLowerCase() : String(word).toLowerCase());

        console.log('开始翻译单词列表:', lowerWords);

        try {
          // 使用翻译服务翻译单词列表
          // 翻译服务会自动处理缓存和本地存储
          const translations = await translator.translateWords(lowerWords, direction);
          console.log('翻译结果:', translations);

          // 创建卡片数据
          for (let i = 0; i < words.length; i++) {
            this.wordCards.push({
              en: words[i], // 使用原始大小写
              zh: translations[i] || '翻译失败',
              flipped: false
            });
          }

          // 保存到本地存储，以便其他页面使用
          // 翻译服务已经自动保存了翻译结果，这里只保存当前的单词列表
          customCardStorage.saveCustomCardData(lowerWords, translations);

        } catch (error) {
          console.error('翻译失败:', error);

          // 显示错误提示
          uni.showToast({
            title: '部分单词翻译失败，请检查网络连接',
            icon: 'none',
            duration: 3000
          });

          // 如果翻译失败，创建没有翻译的卡片
          for (const word of words) {
            this.wordCards.push({
              en: word,
              zh: '翻译失败',
              flipped: false
            });
          }
        }

        // 如果启用了随机顺序，打乱单词顺序
        if (this.isRandom) {
          this.shuffleArray(this.wordCards);
        }

        // 生成分享图片
        this.generateShareImage();
      } catch (error) {
        console.error('Error initializing custom words:', error);
        uni.showToast({
          title: '加载单词失败',
          icon: 'none'
        });
      } finally {
        // 隐藏加载提示
        uni.hideLoading();
      }
    },

    initWords(grade, semester, units) {
      try {
        const gradeData = this.getGradeData(grade);
        if (!gradeData || !gradeData[semester]) {
          throw new Error('No data found for selected grade and semester');
        }

        // 获取选中单元的单词数据
        this.wordCards = [];
        units.forEach(unit => {
          if (gradeData[semester][unit]) {
            const words = gradeData[semester][unit];
            this.wordCards.push(...words.map(word => ({
              ...word,
              flipped: false
            })));
          }
        });

        // 如果启用了随机顺序，打乱单词顺序
        if (this.isRandom) {
          this.shuffleArray(this.wordCards);
        }

        // 生成分享图片
        this.generateShareImage();
      } catch (error) {
        console.error('Error initializing words:', error);
        uni.showToast({
          title: '加载单词失败',
          icon: 'none'
        });
      }
    },

    shuffleArray(array) {
      for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
      }
    },

    handleSwiperChange(e) {
      this.currentIndex = e.detail.current;
      // 如果是听力模式，自动播放新卡片的单词
      if (this.currentMode === 'audio') {
        this.$nextTick(() => {
          this.speakCurrentWord();
        });
      }
    },

    flipCard(index) {
      this.$set(this.wordCards[index], 'flipped', !this.wordCards[index].flipped);

      // 翻转到背面时统一朗读英文
      if (this.wordCards[index].flipped) {

          this.speakCurrentWord();

      }
    },

    prevCard() {
      if (this.currentIndex > 0) {
        this.currentIndex--;
      }
    },

    nextCard() {
      if (this.currentIndex < this.wordCards.length - 1) {
        this.currentIndex++;
      }
    },

    speakCurrentWord() {
      const currentCard = this.wordCards[this.currentIndex];
      if (!currentCard) return;

      // 根据当前模式决定朗读内容
      let textToSpeak = '';
      let lang = '';

      switch (this.currentMode) {
        case 'en':
          textToSpeak = currentCard.en;
          lang = 'en-US';
          break;
        case 'zh':
          textToSpeak = currentCard.en;
          lang = 'en-US';
          break;
        case 'audio':
          textToSpeak = currentCard.en;
          lang = 'en-US';
          break;
      }

      if (lang === 'en-US') {
        // 英文使用有道词典音频
        try {
          const audioContext = uni.createInnerAudioContext();
          audioContext.src = `https://dict.youdao.com/dictvoice?audio=${encodeURIComponent(textToSpeak)}&type=1`;

          audioContext.onError((res) => {
            console.error('Audio error:', res);
            // 播放失败时使用 speech 作为备用
            speech.speak(textToSpeak, {
              lang: lang,
              speed: 0.8
            });
          });

          audioContext.onEnded(() => {
            audioContext.destroy();
          });

          audioContext.play();
        } catch (error) {
          console.error('Audio play error:', error);
          // 出错时使用 speech 作为备用
          speech.speak(textToSpeak, {
            lang: lang,
            speed: 0.8
          });
        }
      } else {
        // 中文使用 speech
        speech.speak(textToSpeak, {
          lang: lang,
          speed: 0.8
        });
      }
    },

    goBack() {
      uni.navigateBack({
        delta: 1 // 返回层级，1 为上一页
      });
    },

    switchMode(mode) {
      this.currentMode = mode;
      // 切换到听力模式时自动播放当前单词
      if (mode === 'audio') {
        this.$nextTick(() => {
          this.speakCurrentWord();
        });
      }
    },

    getFrontContent(card) {
      switch (this.currentMode) {
        case 'en':
          return card.en;
        case 'zh':
          return card.zh;
        case 'audio':
          return '';
      }
    },

    getBackContent(card) {
      // 所有模式下背面都显示英文+中文
      return null; // 因为我们直接在模板中处理了显示逻辑
    },

    getCardTip() {
      switch (this.currentMode) {
        case 'en':
          return '点击卡片查看中文释义';
        case 'zh':
          return '点击卡片查看英文单词';
        case 'audio':
          return '点击卡片查看单词';
      }
    },

    // 检查单词是否在错题本中
    isInErrorBook(word) {
      return errorBookManager.isInErrorBook(word);
    },

    // 添加到错题本
    addToErrorBook(card, e) {
      // 防止事件冒泡到卡片的点击事件
      if (e) e.stopPropagation();

      const word = card.en;
      const isEnglish = /^[a-zA-Z\s]+$/.test(word);

      // 添加到错题本
      const result = errorBookManager.addToErrorBook(word);

      // 显示提示
      uni.showToast({
        title: isEnglish ? '已加入英文错题本' : '已加入中文错题本',
        icon: 'none',
        duration: 2000
      });

      console.log('添加到错题本:', {
        word,
        isEnglish,
        result
      });

      // 强制更新视图
      this.$forceUpdate();
    },

    // 跳转到测试页面
    goToTest() {
      try {
        // 检查是否是自定义卡片模式
        if (this.pageOptions.isCustom === 'true' && this.wordCards.length > 0) {
          // 自定义卡片模式
          // 收集当前卡片的所有英文单词
          const words = this.wordCards.map(card => card.en);

          // 将单词列表转换为 JSON 字符串并编码
          const wordsJson = encodeURIComponent(JSON.stringify(words));

          // 跳转到测试页面，并传递自定义单词列表
          uni.navigateTo({
            url: `/pages/english/test/test?words=${wordsJson}&isCustom=true&mode=en2zh`
          });
        } else {
          // 原有的单元卡片模式
          const { grade, semester, units } = this.pageOptions;

          // 跳转到测试页面，并传递当前选择的单元参数
          uni.navigateTo({
            url: `/pages/english/test/test?grade=${grade}&semester=${semester}&units=${units}&mode=en2zh`
          });
        }
      } catch (error) {
        console.error('跳转到测试页面失败:', error);
        uni.showToast({
          title: '跳转失败，请重试',
          icon: 'none'
        });
      }
    },

    // 跳转到听写页面
    goDictation() {
      try {
        // 检查是否是自定义卡片模式
        if (this.pageOptions.isCustom === 'true' && this.wordCards.length > 0) {
          // 自定义卡片模式
          // 收集当前卡片的所有英文单词
          const words = this.wordCards.map(card => card.en);

          // 将单词列表转换为逗号分隔的字符串
          const wordsStr = words.join(',');

          // 跳转到听写页面，并传递自定义单词列表，始终开启英文模式
          uni.navigateTo({
            url: `/pages/dictation/index?words=${encodeURIComponent(wordsStr)}&isEn=true`
          });
        } else {
          // 原有的单元卡片模式
          const { grade, semester, units } = this.pageOptions;

          // 获取当前单元的所有单词
          const parsedUnits = JSON.parse(decodeURIComponent(units));
          const words = [];

          // 收集当前单元的所有英文单词
          parsedUnits.forEach(unit => {
            const gradeDataObj = this.getGradeData(grade);
            if (gradeDataObj && gradeDataObj[semester] && gradeDataObj[semester][unit]) {
              const unitWords = gradeDataObj[semester][unit];
              unitWords.forEach(word => {
                // 始终使用英文单词进行听写
                words.push(word.en);
              });
            }
          });

          // 将单词列表转换为逗号分隔的字符串
          const wordsStr = words.join(',');

          // 跳转到听写页面，并传递单词列表，始终开启英文模式
          uni.navigateTo({
            url: `/pages/dictation/index?words=${encodeURIComponent(wordsStr)}&isEn=true`
          });
        }
      } catch (error) {
        console.error('跳转到听写页面失败:', error);
        uni.showToast({
          title: '跳转失败，请重试',
          icon: 'none'
        });
      }
    },

    // 更新分享路径
    updateSharePath() {
      let path = '/pages/english/wordcard/learn';

      try {
        // 检查是否是自定义卡片模式
        if (this.pageOptions && this.pageOptions.isCustom === 'true' && this.wordCards.length > 0) {
          // 自定义卡片模式
          // 收集当前卡片的所有英文单词
          const words = this.wordCards.map(card => card.en);

          // 将单词列表转换为 JSON 字符串并编码
          const wordsJson = encodeURIComponent(JSON.stringify(words));

          // 更新分享路径
          path = `${path}?words=${wordsJson}&isCustom=true`;

          // 更新分享标题
          this.share.title = `单词卡片: ${words.slice(0, 3).join(', ')}${words.length > 3 ? '...' : ''}`;
        } else if (this.pageOptions) {
          // 原有的单元卡片模式
          const { grade, semester, units } = this.pageOptions;

          // 更新分享路径
          path = `${path}?grade=${grade}&semester=${semester}&units=${units}`;

          // 更新分享标题
          this.share.title = `单词卡片: ${grade} ${semester} ${JSON.parse(decodeURIComponent(units)).join(', ')}`;
        }

        this.share.path = path;

        console.log('更新后的分享路径:', {
          path: this.share.path,
          title: this.share.title
        });
      } catch (error) {
        console.error('更新分享路径失败:', error);
      }
    },

    // 生成分享图片
    generateShareImage() {
      if (this.isGeneratingImage) return;

      this.isGeneratingImage = true;

      try {
        // 获取要显示的单词列表
        let wordList = [];
        if (this.wordCards.length > 0) {
          // 最多显示 10 个单词
          wordList = this.wordCards.slice(0, 10).map(card => card.en);
        }

        // 将单词列表转换为逗号分隔的字符串
        const wordsStr = wordList.join(',');
        console.log(wordsStr)
        // 使用共享的分享图片生成工具
        generateShareImage(wordsStr, {
          title: '单词卡片',  // 使用单词卡片作为标题
          showDate: true,      // 不显示日期
          delimiter: ', '       // 使用英文逗号+空格作为分隔符
        }).then(tempFilePath => {
          this.share.imageUrl = tempFilePath;
          console.log('生成分享图片成功:', tempFilePath);
          this.isGeneratingImage = false;
        }).catch(error => {
          console.error('生成分享图片失败:', error);
          this.isGeneratingImage = false;
        });
      } catch (error) {
        console.error('生成分享图片失败:', error);
        this.isGeneratingImage = false;
      }
    }
  }
}
</script>
// 当前页面局部样式
<style scoped>
.header{
  display: flex;
  justify-content: space-between;
  z-index: 99;
  position: relative;
}
.container {
    background-color: #f8f8f8;
    min-height: 100vh;
    position: relative;
    box-sizing: border-box;
  }

  .mode-selector {
    display: flex;
    gap: 5rpx;
    background-color: #fff;
    padding: 6rpx;
    border-radius: 30rpx;
    box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
  }

  .mode-item {
    padding: 8rpx 15rpx;
    font-size: 24rpx;
    color: #666;
    border-radius: 20rpx;
    transition: all 0.3s;
  }

  .mode-item.active {
    background-color: #393C98;
    color: #fff;
  }

  .progress {
    font-size: 28rpx;
    color: #666;
    background-color: #fff;
    padding: 10rpx 15rpx;
    border-radius: 30rpx;
    box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
  }

  .content {
    position: absolute;
    height: calc(100vh - 150rpx);
    padding: 20rpx 0;
    top:0;
    left:0;
    right:0;
  }

  .card-swiper {
    height: 100%;
    width: 100%;
  }

  .card-container {
    position: relative;
    top:8%;
    margin: 0 40rpx;
    height: 86%;
    position: relative;
    perspective: 2000rpx;
  }

  .card-front, .card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    transition: transform 0.6s;
    border-radius: 30rpx;
    box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 40rpx;
    box-sizing: border-box;
  }

  .card-front {
    background-color: #fff;
    transform: rotateY(0deg);
  }

  .card-back {
    background-color: #393C98;
    color: #fff;
    transform: rotateY(180deg);
  }

  .card-container.flipped .card-front {
    transform: rotateY(180deg);
  }

  .card-container.flipped .card-back {
    transform: rotateY(0deg);
  }

  .card-type {
    font-size: 28rpx;
    color: #8B648B;
    margin-bottom: 40rpx;
  }

  .card-back .card-type {
    color: #fff;
    opacity: 0.8;
  }

  .card-content {
    font-size: 72rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 60rpx;
    text-align: center;
  }

  .card-back .card-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20rpx;
    text-align: center;
  }

  .back-en {
    font-size: 72rpx;
    font-weight: 600;
    color: #fff;
    margin-bottom: 20rpx;
  }

  .back-zh {
    font-size: 48rpx;
    color: rgba(255, 255, 255, 0.9);
  }

  .card-tip {
    font-size: 24rpx;
    color: #999;
    position: absolute;
    bottom: 40rpx;
    left:50%;
    transform: translateX(-50%);
    width: auto;
    text-align: center;
  }

  .card-back .card-tip {
    color: #fff;
    opacity: 0.7;
  }

  .add-to-error-book {
    background-color: rgba(255, 255, 255, 0.2);
    padding: 20rpx 30rpx;
    border-radius: 10rpx;
    font-weight: 500;
    opacity: 1 !important;
    cursor: pointer;
  }

  .add-to-error-book:active {
    background-color: rgba(255, 255, 255, 0.3);
  }

  .in-error-book {
    padding: 10rpx 30rpx;
    border-radius: 30rpx;
    font-weight: 500;
    opacity: 1 !important;
    color: #fff;
  }

  .action-btns {
    display: flex;
    justify-content: center;

    align-items: center;
  }



  .controls {
    padding: 30rpx;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-around;
    align-items: center;
  }

  .control-btn {
    width: 160rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f0f0f0;
    border-radius: 40rpx;
    font-size: 28rpx;
    color: #333;
  }

  .control-btn.disabled {
    opacity: 0.5;
  }

  .speak-btn {
    background-color: #393C98;
    color: #fff;
    width: 200rpx;
  }

  .test-btn, .dictation-btn, .share-btn {
    color: #393C98;
    padding:0rpx 10rpx;
    display: flex;
    justify-items: center;
    align-items: center;
    font-size:25rpx;
  }

  .share-button {
    background: none;
    border: none;
    margin: 0;
    padding: 0;
    color: #393C98;
    font-size: 25rpx;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .share-button::after {
    border: none;
  }

  .audio-mode {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 20rpx;
  }

  .audio-icon {
    font-size: 80rpx;
    color: #393C98;
  }
</style>