<template>
  <view class="dictation-container">
    <view class="menu-list">
      <!-- 自定义听写 -->
      <view class="menu-item" @click="navigateToCustomDictation">
        <text class="menu-title">自定义听写</text>
        <text class="menu-desc">支持(中/英)自定义内容听写</text>
        <text class="arrow">›</text>
      </view>
      
      <!-- 英语课内听写 -->
      <view class="menu-item" @click="navigateToEnglishDictation">
        <text class="menu-title">英语课内听写</text>
        <text class="menu-desc">根据教材内容进行听写练习</text>
        <text class="arrow">›</text>
      </view>
    </view>
    
    <!-- 返回首页 -->
    <view class="back-btn" @click="goBack">
      <text>返回首页</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {}
  },
  methods: {
    // 跳转到自定义听写
    navigateToCustomDictation() {
      uni.navigateTo({
        url: '/pages/dictation/index'
      });
    },
    
    // 跳转到英语课内听写
    navigateToEnglishDictation() {
      uni.navigateTo({
        url: '/pages/english/common/select?type=dictation'
      });
    },
    
    // 返回首页
    goBack() {
      uni.reLaunch({
        url: '/pages/index/index'
      });
    }
  }
}
</script>

<style scoped>
.dictation-container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding: 30rpx;
}



.title {
  font-size: 48rpx;
  font-weight: 600;
  color: #393C98;
}

.menu-list {
  margin-bottom: 40rpx;
}

.menu-item {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  position: relative;
}

.menu-title {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}

.menu-desc {
  font-size: 28rpx;
  color: #666;
}

.arrow {
  position: absolute;
  right: 50rpx;
  font-size: 48rpx;
  color: #393C98;
  font-weight: 300;
}

.menu-item:active {
  transform: scale(0.98);
  transition: transform 0.2s;
}


</style> 