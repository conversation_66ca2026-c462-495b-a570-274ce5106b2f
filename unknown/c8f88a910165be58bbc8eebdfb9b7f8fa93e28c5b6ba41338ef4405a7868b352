// 生成分享图片的工具方法
export const generateShareImage = (words, options = {}) => {
    // 默认配置
    const config = {
        title: '今日听写', // 默认标题
        showDate: true,     // 是否显示日期
        delimiter: '、',  // 单词之间的分隔符
        ...options
    };
    return new Promise((resolve, reject) => {
        // 创建 canvas 上下文
        const ctx = uni.createCanvasContext('shareCanvas');

        // 绘制白色背景
        ctx.setFillStyle('#ffffff');
        ctx.fillRect(0, 0, 400, 320);

        // 绘制紫色圆角矩形
        const radius = 10;
        const startX = 5;
        const startY = 5;
        const width = 390;
        const height = 310;

        // 绘制填充的紫色圆角矩形
        ctx.beginPath();
        // 左上角
        ctx.moveTo(startX + radius, startY);
        // 上边
        ctx.lineTo(startX + width - radius, startY);
        // 右上角
        ctx.arc(startX + width - radius, startY + radius, radius, -Math.PI/2, 0);
        // 右边
        ctx.lineTo(startX + width, startY + height - radius);
        // 右下角
        ctx.arc(startX + width - radius, startY + height - radius, radius, 0, Math.PI/2);
        // 下边
        ctx.lineTo(startX + radius, startY + height);
        // 左下角
        ctx.arc(startX + radius, startY + height - radius, radius, Math.PI/2, Math.PI);
        // 左边
        ctx.lineTo(startX, startY + radius);
        // 左上角
        ctx.arc(startX + radius, startY + radius, radius, Math.PI, -Math.PI/2);

        ctx.closePath();
        // 填充紫色
        ctx.setFillStyle('#f1f1f1');
        ctx.fill();

        // 设置标题
        let titleText = config.title;

        // 如果需要显示日期
        if (config.showDate) {
            const today = new Date();
            const dateStr = `${today.getFullYear()}年${today.getMonth() + 1}月${today.getDate()}日`;
            titleText = `${titleText} ${dateStr}`;
        }

        ctx.setFillStyle('#382BA7');
        ctx.setFontSize(30);
        // 尝试设置字体粗细
        try {
            ctx.setFontWeight && ctx.setFontWeight('bold');
        } catch (e) {
            console.log('当前环境不支持设置字体粗细');
        }
        ctx.fillText(titleText, 20, 45);

        // 设置听写内容
        ctx.setFontSize(26);
        // 尝试设置字体粗细
        try {
            ctx.setFontWeight && ctx.setFontWeight('bold');
        } catch (e) {
            console.log('当前环境不支持设置字体粗细');
        }
        const maxWidth = 360; // 考虑左右边距
        const lineHeight = 35; // 减小行高
        let y = 90; // 调整起始位置，缩小与标题的间距

        // 文本换行处理
        const wordsArray = words.split(/[,，\n]+/).map(word => word.trim()).filter(word => word);
        let currentLine = '';

        wordsArray.forEach((word, index) => {
            const testLine = currentLine + (currentLine ? config.delimiter : '') + word;
            const metrics = ctx.measureText(testLine);

            if (metrics.width > maxWidth) {
                // 当前行已满，绘制并开始新行
                ctx.fillText(currentLine, 20, y);
                y += lineHeight;
                currentLine = word;
            } else {
                currentLine = testLine;
            }

            // 如果是最后一个词，绘制当前行
            if (index === wordsArray.length - 1) {
                ctx.fillText(currentLine, 20, y);
            }
        });

        // 将 canvas 转换为图片
        ctx.draw(false, () => {
            setTimeout(() => {
                uni.canvasToTempFilePath({
                    canvasId: 'shareCanvas',
                    success: (res) => {
                        resolve(res.tempFilePath);
                    },
                    fail: (error) => {
                        console.error('生成分享图片失败:', error);
                        reject(error);
                    }
                });
            }, 100);
        });
    });
};